现在需要建设每个工作流的页面！每一个工作流都要有自己独立的设置页面。

# 任务1：修改工作流卡片按钮

目前每个工作流卡片的下方都有"运行"和”编辑“两个按钮，用户不能在列表里直接运行某个工作流！因此，把它们改写成一个按钮”转到工作流“。

当用户在工作流标签下点击”转到工作流“按钮之后，就要跳转到该工作流专用的配置页面。

# 任务2：设计实现工作流页面的架构

每个工作流页面要实现以下功能和内容

## 1. 标题

工作流名称

## 2. 简介

读取工作流中的 Note 或 Markdown Note 节点的内容，整理成美观的Markdown格式，显示在简介位置

## 3. 关键设置参数设计与呈现

### 3.0 对所有工作流的编辑功能设计

要提供**关键模块**的编辑设定功能，每个关键模块设计一个标签，样式参考首页的工作流呈现的样式。

- 举例来说，图生图或者图片编辑都需要编辑提示词，因此需要将工作流中关键的节点（如Clip文本节点）呈现在界面上，让用户编辑提示词，用户输入之后，传递到工作流中对应的节点，然后按用户输入内容执行新的工作流，但是这些设定和编辑功能都是默认非激活状态的，需要提供一个选择框来提示用户“编辑该节点有风险，确认要编辑？”，用户勾选之后，该编辑界面才能处于激活状态，才会可用！

- 除了上述举例的 Clip 文本编辑，再举一个例子，如涉及到抠图的节点，如使用了`SegemntAnything V2`这类节点，其中需要指定抠出的对象，也需要提供这个节点内“提示”参数的设定界面！

- 还有 K采样器中，相关的“CFG”、“步数”、“采样器名称”、“调度器”等参数，也需要提供相应的设定界面，控件的设定风格按照ComfyUI中对应参数的风格，原本是数字输入，就设置输入框，原本是下拉菜单就设置下拉菜单；

- 总之，原则就是，除了模型选择（加载）类、纯说明类的节点外，所有节点都要以卡片方式呈现在编辑界面上，然后每个卡片通过前面所述的“选择框”来控制是否激活编辑！

### 3.1 特殊功能：对于图生图、图片编辑、图生视频类型的工作流：

- 要提供一个设定图片输入文件夹的按钮，用户将需要加工的图片放置在这个文件夹中，运行就可以对该目录中的所有图片遍历执行本工作流;

- 要提供设定图片输出文件夹的按钮，该工作流生成的图片都放在其中！

### 3.2 特殊功能：对于文生图、文生视频类型的工作流：

- 最重要的就是文本提示信息，因此只需要遵循 3.0 节中所述的共性编辑功能中的 Clip 文本编辑就可以了；

- 要提供设定图片输出、视频输出目录的按钮，该工作流生成的图片、视频都放在其中！



---

因为我访问的工作流其实是由两个应用同时访问的，一个是本程序，另一个是ComfyUI应用，它也加载本工作流进行图像操作！所以二者其实都可以更改本工作流文件。

因此我希望实现的最终效果是，两个应用能够各自读取/写入工作流文件，而且，它们之间不论谁修改了工作流，另一方都能够重载工作流文件（可以自动也可以手动，但是至少要有监测改动的一个机制），ComfyUI 一端我们无权做操作和开发，因此要保证本项目中能够实现这个自动监测改变，并能够保持工作流为最新状态的！

进一步来说，后面本项目涉及到局域网部署或者网络部署，ComfyUI是部署在网络中的，仅通过 IP:PORT 的形式进行访问，那么