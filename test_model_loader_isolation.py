#!/usr/bin/env python3
"""
Test Model Loader Isolation
Verifies that model loader nodes are properly excluded from parameter editing
"""
import json
import sys
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.ui.workflows.workflow_utils import analyze_workflow_nodes, is_editable_node, is_model_loader_node

def test_model_loader_isolation():
    """Test that model loader nodes are properly isolated from editing"""
    
    print("🧪 Testing Model Loader Node Isolation")
    print("=" * 50)
    
    # Create a test workflow with problematic node types
    test_workflow = {
        "1": {
            "inputs": {
                "text": "test prompt",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Text Prompt"}
        },
        "2": {
            "inputs": {
                "clip_name1": "Flux/clip_l.safetensors",
                "clip_name2": "Flux/t5xxl_fp16.safetensors"
            },
            "class_type": "DualCLIPLoader",
            "_meta": {"title": "CLIP Loader"}
        },
        "3": {
            "inputs": {
                "unet_name": "Flux/flux1-dev-kontext_fp8_scaled.safetensors"
            },
            "class_type": "UNETLoader", 
            "_meta": {"title": "UNET Loader"}
        },
        "4": {
            "inputs": {
                "vae_name": "Flux/ae.sft"
            },
            "class_type": "VAELoader",
            "_meta": {"title": "VAE Loader"}
        },
        "5": {
            "inputs": {
                "lora_name": "kontext_毛毯提取图案印花_1.0",
                "strength_model": 1.0
            },
            "class_type": "LoraLoaderModelOnly",
            "_meta": {"title": "LoRA Loader"}
        },
        "6": {
            "inputs": {
                "model_name": "R-ESRGAN_4x+ Anime6B"
            },
            "class_type": "UpscaleModelLoader",
            "_meta": {"title": "Upscale Model Loader"}
        },
        "7": {
            "inputs": {
                "image": "ComfyUI_00001_bmctb_1752857357Ikskfi.png"
            },
            "class_type": "LoadImage",
            "_meta": {"title": "Load Image"}
        },
        "8": {
            "inputs": {
                "steps": 20,
                "cfg": 7.0,
                "seed": 42
            },
            "class_type": "KSampler",
            "_meta": {"title": "Sampler"}
        }
    }
    
    print(f"📊 Test workflow created with {len(test_workflow)} nodes")
    
    # Test node classification
    print(f"\n🔍 Testing Node Classification:")
    model_loader_nodes = []
    editable_nodes = []
    
    for node_id, node_data in test_workflow.items():
        class_type = node_data.get("class_type", "")
        title = node_data.get("_meta", {}).get("title", class_type)
        
        is_model = is_model_loader_node(class_type)
        is_editable = is_editable_node(class_type)
        
        print(f"   Node {node_id} ({class_type}): Model={is_model}, Editable={is_editable}")
        
        if is_model:
            model_loader_nodes.append((node_id, class_type, title))
        if is_editable:
            editable_nodes.append((node_id, class_type, title))
    
    print(f"\n📋 Classification Results:")
    print(f"   Model Loaders: {len(model_loader_nodes)}")
    print(f"   Editable Nodes: {len(editable_nodes)}")
    
    # Test parameter extraction
    print(f"\n🔧 Testing Parameter Extraction:")
    analysis = analyze_workflow_nodes(test_workflow)
    extracted_editable = analysis.get("editable_nodes", [])
    extracted_models = analysis.get("model_nodes", [])
    
    print(f"   Extracted Editable: {len(extracted_editable)}")
    print(f"   Extracted Models: {len(extracted_models)}")
    
    # Check for contamination
    print(f"\n🚨 Checking for Parameter Contamination:")
    contaminated_nodes = []
    
    for editable_node in extracted_editable:
        node_id = editable_node["id"]
        class_type = editable_node["class_type"]
        
        # Check if this is a model loader node that shouldn't be editable
        if is_model_loader_node(class_type):
            contaminated_nodes.append((node_id, class_type, editable_node["title"]))
            print(f"   🚨 CONTAMINATED: Node {node_id} ({class_type}) should NOT be editable!")
            
            # Show what parameters would be extracted
            for param in editable_node["editable_params"]:
                print(f"      - {param['path']}: {param['current_value']}")
    
    if not contaminated_nodes:
        print(f"   ✅ No contamination detected - model loaders properly excluded")
    else:
        print(f"   ❌ CRITICAL: {len(contaminated_nodes)} model loader nodes incorrectly marked as editable!")
    
    # Show what should be editable
    print(f"\n✅ Correctly Editable Nodes:")
    for editable_node in extracted_editable:
        if not is_model_loader_node(editable_node["class_type"]):
            print(f"   ✅ Node {editable_node['id']} ({editable_node['class_type']}): {editable_node['title']}")
            for param in editable_node["editable_params"]:
                print(f"      - {param['path']}: {param['current_value']}")
    
    # Test the specific problematic values
    print(f"\n🔍 Testing Specific Problematic Values:")
    problematic_values = {
        "unet_name": "Flux/flux1-dev-kontext_fp8_scaled.safetensors",
        "clip_name1": "clip_l.safetensors", 
        "clip_name2": "t5xxl_fp16.safetensors",
        "vae_name": "Flux/ae.sft",
        "lora_name": "kontext_毛毯提取图案印花_1.0",
        "model_name": "R-ESRGAN_4x+ Anime6B"
    }
    
    for param_name, original_value in problematic_values.items():
        print(f"   {param_name}: {original_value}")
        
        # Check if this would be processed as editable
        for editable_node in extracted_editable:
            for param in editable_node["editable_params"]:
                if param.get("param_name") == param_name:
                    print(f"     🚨 FOUND in editable params: {param['path']}")
    
    return len(contaminated_nodes) == 0

def test_node_type_classification():
    """Test individual node type classification"""
    
    print(f"\n🧪 Testing Individual Node Type Classification:")
    print("=" * 50)
    
    test_cases = [
        ("DualCLIPLoader", True, False),  # Should be model loader, not editable
        ("VAELoader", True, False),
        ("UNETLoader", True, False), 
        ("LoraLoaderModelOnly", True, False),
        ("UpscaleModelLoader", True, False),
        ("CheckpointLoaderSimple", True, False),
        ("CLIPTextEncode", False, True),  # Should be editable, not model loader
        ("KSampler", False, True),
        ("FluxGuidance", False, True),
        ("EmptyLatentImage", False, True)
    ]
    
    all_correct = True
    
    for node_type, should_be_model, should_be_editable in test_cases:
        is_model = is_model_loader_node(node_type)
        is_editable = is_editable_node(node_type)
        
        model_correct = is_model == should_be_model
        editable_correct = is_editable == should_be_editable
        
        status = "✅" if (model_correct and editable_correct) else "❌"
        print(f"   {status} {node_type}:")
        print(f"      Model Loader: {is_model} (expected: {should_be_model}) {'✅' if model_correct else '❌'}")
        print(f"      Editable: {is_editable} (expected: {should_be_editable}) {'✅' if editable_correct else '❌'}")
        
        if not (model_correct and editable_correct):
            all_correct = False
    
    return all_correct

if __name__ == "__main__":
    print("🚀 Starting Model Loader Isolation Tests")
    print("=" * 60)
    
    # Test 1: Node type classification
    classification_ok = test_node_type_classification()
    
    # Test 2: Parameter extraction isolation
    isolation_ok = test_model_loader_isolation()
    
    print(f"\n🏁 Test Results Summary:")
    print(f"   Node Classification: {'✅ PASS' if classification_ok else '❌ FAIL'}")
    print(f"   Parameter Isolation: {'✅ PASS' if isolation_ok else '❌ FAIL'}")
    
    if classification_ok and isolation_ok:
        print(f"\n🎉 ALL TESTS PASSED - Model loader contamination bug is FIXED!")
    else:
        print(f"\n🚨 TESTS FAILED - Bug still exists, needs further investigation")
