import pytest
from fastapi.testclient import TestClient
import os
import tempfile
import json
from pathlib import Path

# 设置测试环境变量
os.environ["AUTH_ENABLED"] = "false"  # 测试时禁用认证
os.environ["COMFYUI_BASE_URL"] = "http://localhost:8188"
os.environ["DATA_DIR"] = tempfile.mkdtemp()

from app.api.main import app

@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)

@pytest.fixture
def auth_headers():
    """创建认证头（测试时为空）"""
    return {}

def test_health_check(client):
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["success"] is True
    assert "data" in data
    assert "timestamp" in data["data"]

def test_login_endpoint_disabled_auth(client):
    """测试禁用认证时的登录端点"""
    login_data = {
        "username": "test_user",
        "access_key": "test_key"
    }
    
    response = client.post("/auth/login", json=login_data)
    # 认证禁用时应该返回成功
    assert response.status_code == 200
    
    data = response.json()
    assert "token" in data
    assert "user" in data

def test_workflows_endpoint(client, auth_headers):
    """测试工作流列表端点"""
    response = client.get("/workflows", headers=auth_headers)
    assert response.status_code == 200
    
    # 应该返回工作流列表（可能为空）
    data = response.json()
    assert isinstance(data, list)

def test_demo_workflow_exists(client, auth_headers):
    """测试演示工作流存在"""
    response = client.get("/workflows/demo_text_generation", headers=auth_headers)
    
    if response.status_code == 200:
        data = response.json()
        assert data["key"] == "demo_text_generation"
        assert "metadata" in data
        assert data["metadata"]["name"] == "演示文本生成"
        assert "RDY" in data["metadata"]["tags"]

def test_unauthorized_access_with_auth_enabled():
    """测试启用认证时的未授权访问"""
    # 临时启用认证
    os.environ["AUTH_ENABLED"] = "true"
    
    from app.api.main import app
    client = TestClient(app)
    
    response = client.get("/workflows")
    assert response.status_code == 401
    
    # 恢复测试环境
    os.environ["AUTH_ENABLED"] = "false"

def test_qr_login_endpoints_return_501(client, auth_headers):
    """测试扫码登录端点返回 501"""
    # 测试各个占位端点
    endpoints = [
        ("POST", "/auth/qr/init"),
        ("GET", "/auth/qr/status?login_id=test"),
        ("POST", "/auth/qr/redeem"),
        ("POST", "/auth/bind")
    ]
    
    for method, endpoint in endpoints:
        if method == "POST":
            response = client.post(endpoint, json={}, headers=auth_headers)
        else:
            response = client.get(endpoint, headers=auth_headers)
        
        assert response.status_code == 501
        assert "未实现" in response.json().get("detail", "")

def test_run_workflow_missing_workflow(client, auth_headers):
    """测试运行不存在的工作流"""
    run_data = {
        "workflow_key": "nonexistent_workflow",
        "parameters": {}
    }
    
    response = client.post("/run", json=run_data, headers=auth_headers)
    assert response.status_code == 404

def test_task_status_nonexistent(client, auth_headers):
    """测试查询不存在的任务状态"""
    response = client.get("/status/nonexistent_task_id", headers=auth_headers)
    assert response.status_code == 404

def test_outputs_endpoint(client, auth_headers):
    """测试输出文件端点"""
    response = client.get("/outputs", headers=auth_headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["success"] is True
    assert "data" in data
