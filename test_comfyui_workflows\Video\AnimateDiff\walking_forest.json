{"3": {"inputs": {"seed": 12345, "steps": 50, "cfg": 10.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>"}, "4": {"inputs": {"ckpt_name": "animatediff_v3.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage"}, "6": {"inputs": {"text": "a person walking through a forest", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}, "7": {"inputs": {"text": "static, blurry, low quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}}