#!/usr/bin/env python3
"""
测试登录修复 - 验证前端登录功能是否正常工作
"""

import requests
import json
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

def test_login():
    """测试登录功能"""
    api_port = os.getenv('API_PORT', '18001')
    api_base_url = f"http://localhost:{api_port}"
    
    print(f"API URL: {api_base_url}")
    print("=" * 50)
    
    # 测试健康检查
    try:
        health_resp = requests.get(f"{api_base_url}/health", timeout=10)
        print(f"健康检查: {health_resp.status_code}")
        if health_resp.status_code == 200:
            health_data = health_resp.json()
            print(f"  认证启用: {health_data['data']['auth_enabled']}")
            print(f"  ComfyUI: {health_data['data']['comfyui_connected']}")
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False
    
    print()
    
    # 测试登录 - 错误凭据
    print("测试错误凭据:")
    try:
        login_data = {"username": "admin", "access_key": "wrong-key"}
        response = requests.post(f"{api_base_url}/auth/login", json=login_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code != 200:
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", "未知错误")
                print(f"  错误信息: {error_detail}")
            except ValueError as e:
                print(f"  JSON解析错误: {e}")
                print(f"  原始响应: {repr(response.text[:100])}")
    except Exception as e:
        print(f"  请求失败: {e}")
    
    print()
    
    # 测试登录 - 正确凭据
    print("测试正确凭据:")
    try:
        login_data = {"username": "admin", "access_key": "demo-admin-key-12345"}
        response = requests.post(f"{api_base_url}/auth/login", json=login_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("  ✓ 登录成功!")
            print(f"  用户: {result['user']['username']}")
            print(f"  角色: {result['user']['roles']}")
            print(f"  令牌长度: {len(result['token'])}")
            return True
        else:
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", "未知错误")
                print(f"  错误信息: {error_detail}")
            except ValueError as e:
                print(f"  JSON解析错误: {e}")
                print(f"  原始响应: {repr(response.text[:100])}")
    except Exception as e:
        print(f"  请求失败: {e}")
    
    return False

if __name__ == "__main__":
    print("FreemanWorkHub 登录测试")
    print("=" * 50)
    
    success = test_login()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 所有测试通过! 登录功能正常工作。")
        print("\n请使用以下凭据登录:")
        print("  用户名: admin")
        print("  访问密钥: demo-admin-key-12345")
    else:
        print("✗ 测试失败，请检查API服务器状态。")
