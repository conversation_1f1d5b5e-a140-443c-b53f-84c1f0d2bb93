# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Environment files
.env*
!.env.example

# Auth keys (real ones, not examples)
app/config/auth_keys.yaml
app/config/whitelist.json
app/config/whitelist_pubkey.pem

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Data directories
app/data/inputs/
app/data/outputs/
app/data/temp/

# Logs
*.log
logs/

# Streamlit
.streamlit/
