#!/usr/bin/env python3
"""
Debug Parameter Contamination
Investigates why unedited model loader nodes are being modified
"""
import json
import sys
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.ui.workflows.workflow_utils import analyze_workflow_nodes, is_editable_node, is_model_loader_node
from app.api.comfy_client import ComfyUIClient

def analyze_parameter_contamination():
    """Analyze why model loader parameters are being contaminated"""
    
    print("🔍 Analyzing Parameter Contamination Bug")
    print("=" * 50)
    
    # Load the problematic workflow
    workflow_path = Path("app/config/workflows/RDY_pattern-extraction/workflow.json")
    
    if not workflow_path.exists():
        print(f"❌ Workflow file not found: {workflow_path}")
        return
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow_json = json.load(f)
        
        print(f"✅ Loaded workflow with {len(workflow_json)} nodes")
        
        # Analyze all nodes
        print(f"\n📊 Node Analysis:")
        model_loaders = []
        editable_nodes = []
        other_nodes = []
        
        for node_id, node_data in workflow_json.items():
            if not isinstance(node_data, dict):
                continue
                
            class_type = node_data.get("class_type", "")
            node_title = node_data.get("_meta", {}).get("title", class_type)
            inputs = node_data.get("inputs", {})
            
            print(f"\n🔧 Node {node_id}: {node_title} ({class_type})")
            
            # Check node classification
            is_model = is_model_loader_node(class_type)
            is_editable = is_editable_node(class_type)
            
            print(f"   Model Loader: {'✅' if is_model else '❌'}")
            print(f"   Editable: {'✅' if is_editable else '❌'}")
            
            # Show inputs
            print(f"   Inputs:")
            for input_name, input_value in inputs.items():
                if isinstance(input_value, list):
                    print(f"     {input_name}: [connection] -> {input_value}")
                else:
                    print(f"     {input_name}: {input_value}")
            
            # Categorize
            if is_model:
                model_loaders.append((node_id, class_type, node_title, inputs))
            elif is_editable:
                editable_nodes.append((node_id, class_type, node_title, inputs))
            else:
                other_nodes.append((node_id, class_type, node_title, inputs))
        
        print(f"\n📋 Classification Summary:")
        print(f"   Model Loaders: {len(model_loaders)}")
        print(f"   Editable Nodes: {len(editable_nodes)}")
        print(f"   Other Nodes: {len(other_nodes)}")
        
        # Test parameter extraction
        print(f"\n🧪 Testing Parameter Extraction:")
        analysis = analyze_workflow_nodes(workflow_json)
        extracted_editable = analysis.get("editable_nodes", [])
        extracted_models = analysis.get("model_nodes", [])
        
        print(f"   Extracted Editable: {len(extracted_editable)}")
        print(f"   Extracted Models: {len(extracted_models)}")
        
        # Show what parameters would be extracted
        print(f"\n📝 Extracted Editable Parameters:")
        all_param_paths = []
        for node in extracted_editable:
            node_id = node["id"]
            node_title = node["title"]
            print(f"   Node {node_id} ({node_title}):")
            for param in node["editable_params"]:
                param_path = param["path"]
                current_value = param["current_value"]
                all_param_paths.append(param_path)
                print(f"     - {param_path}: {current_value}")
        
        # Test what happens when we apply test parameters
        print(f"\n🧪 Testing Parameter Update Logic:")
        test_parameters = {}
        for path in all_param_paths[:3]:  # Test first 3 parameters
            test_parameters[path] = f"TEST_VALUE_{path.replace('.', '_')}"
        
        print(f"Test parameters: {test_parameters}")
        
        # Apply the update
        client = ComfyUIClient()
        updated_workflow = client.update_workflow_parameters(workflow_json, test_parameters)
        
        # Check what changed
        print(f"\n🔍 Checking for Unintended Changes:")
        changes_found = False
        
        for node_id, original_node in workflow_json.items():
            if node_id not in updated_workflow:
                print(f"❌ Node {node_id} was removed!")
                changes_found = True
                continue
            
            updated_node = updated_workflow[node_id]
            original_inputs = original_node.get("inputs", {})
            updated_inputs = updated_node.get("inputs", {})
            
            # Check for changes in inputs
            for input_name, original_value in original_inputs.items():
                updated_value = updated_inputs.get(input_name)
                
                if updated_value != original_value:
                    # Check if this was an intended change
                    param_path = f"{node_id}.inputs.{input_name}"
                    if param_path not in test_parameters:
                        print(f"🚨 UNINTENDED CHANGE in Node {node_id} ({original_node.get('class_type', '')}):")
                        print(f"   {input_name}: {original_value} → {updated_value}")
                        print(f"   Path: {param_path} (NOT in test parameters)")
                        changes_found = True
                    else:
                        print(f"✅ Intended change in Node {node_id}:")
                        print(f"   {input_name}: {original_value} → {updated_value}")
        
        if not changes_found:
            print(f"✅ No unintended changes detected")
        else:
            print(f"🚨 CRITICAL: Unintended parameter changes detected!")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_parameter_contamination()
