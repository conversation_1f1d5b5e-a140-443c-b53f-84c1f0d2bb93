# FreemanWorkHub

ComfyUI 工作流管理与自动化平台

## 功能特性

本项目旨在简化 ComfyUI 工作流的管理和使用，让不熟悉 ComfyUI 操作的用户也能轻松使用预设工作流，并支持工作流的组合与自动化管道操作。

**核心功能：**
- 🔍 浏览和管理已有工作流，支持 DEV/RDY 标签筛选
- 🎯 **专用工作流配置页面**：每个工作流都有独立的配置和运行界面
- ⚙️ **智能参数编辑**：自动识别可编辑参数（文本、数值、选择等），安全模式防止误操作
- 📖 **工作流简介显示**：自动提取工作流中的Note节点内容作为说明
- 📁 **智能目录管理**：根据工作流类型自动配置输入输出目录
- 🔗 组合多个工作流构建复杂处理管线
- 🔐 基于订阅的访问控制（username + AccessKey）
- 📂 直接读取ComfyUI工作流目录，无需本地存储

## 快速开始

### 环境要求
- Python 3.8+
- Windows PowerShell 5.1+
- ComfyUI 服务器（可选，用于实际工作流执行）

### 安装与配置

```powershell
# 1. 克隆项目
git clone <repository-url>
cd FreemanWorkHub

# 2. 创建虚拟环境
python -m venv .venv
.\.venv\Scripts\Activate.ps1

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
copy .env.example .env
# 编辑 .env 文件，设置 COMFYUI_BASE_URL 等参数

# 5. 配置认证密钥（可选）
copy app\config\auth_keys.example.yaml app\config\auth_keys.yaml
# 编辑 auth_keys.yaml 添加用户和访问密钥
```

### 配置ComfyUI工作流目录

FreemanWorkHub 直接读取ComfyUI的工作流文件，无需在项目内创建工作流目录：

1. **找到ComfyUI的工作流目录**
   - 通常位于：`C:\Users\<USER>\Documents\ComfyUI\user\workflows\`
   - 或其他你保存工作流JSON文件的位置

2. **在FreemanWorkHub中设置目录**
   - 启动应用后，进入"⚙️ 工作流管理"页面
   - 在"ComfyUI 工作流目录设置"区域输入目录路径
   - 点击"🔄 同步工作流"按钮加载工作流

3. **工作流文件格式**
   - 支持所有 `.json` 格式的ComfyUI工作流文件
   - 系统会自动提取可编辑参数（如文本输入、数值参数等）
   - 工作流默认标记为"RDY"状态，可直接使用

### 启动服务

```powershell
# 启动后端 API（端口 8001）
uvicorn app.api.main:app --reload --port 8001

# 新开终端，启动前端 UI（端口 8501）
.\.venv\Scripts\Activate.ps1
streamlit run app/ui/Home.py --server.port 8501
```

访问 http://localhost:8501 使用 Streamlit 界面，或 http://localhost:8001/docs 查看 API 文档。

### 运行测试

```powershell
pytest -q
```

## 架构概览

```
app/
├── ui/                     # Streamlit 前端界面
│   ├── Home.py            # 主页面（登录 + Gallery + 工作流配置）
│   └── workflows/         # 工作流配置模块
│       ├── config_page.py    # 工作流配置页面
│       └── workflow_utils.py # 工作流分析工具
├── api/                   # FastAPI 后端服务
│   ├── main.py           # API 入口
│   ├── auth.py           # 认证与授权
│   ├── comfy_client.py   # ComfyUI 客户端
│   └── schemas.py        # 数据模型
├── services/             # 业务逻辑层
├── config/               # 配置文件
│   ├── auth_keys.yaml    # 访问密钥（不提交）
│   └── workflows/        # 工作流定义（示例）
└── data/                 # 数据目录
    ├── inputs/           # 输入文件
    └── outputs/          # 输出结果
```

## 工作流配置功能

### 新版工作流管理
从v1.1开始，每个工作流都有专用的配置页面，提供以下功能：

**🎯 智能参数识别**
- 自动识别可编辑参数（CLIPTextEncode、KSampler、SegmentAnything等）
- 根据参数类型提供合适的编辑控件（文本框、数值输入、下拉选择等）
- 安全模式：每个节点需要用户确认后才能编辑

**📖 工作流简介**
- 自动提取工作流中Note和Markdown Note节点的内容
- 以Markdown格式美观展示工作流说明

**📁 智能目录配置**
- 根据工作流类型自动配置输入输出目录
- 文生图/文生视频：只需设置输出目录
- 图生图/图生视频：需设置输入和输出目录
- 支持浏览选择和手动创建目录

**🔧 安全机制**
- DEV标签工作流仅供查看，无法运行
- 所有编辑操作都有风险警告和确认机制
- 参数路径可视化，便于调试

### 工作流类型支持
- ✅ **文生图**：CLIPTextEncode参数编辑 + 图片输出目录
- ✅ **图生图**：图片输入目录 + 图片输出目录 + 参数编辑  
- ✅ **文生视频**：CLIPTextEncode参数编辑 + 视频输出目录
- ✅ **图生视频**：图片输入目录 + 视频输出目录 + 参数编辑

## 配置说明

### 认证配置
项目支持基于 AccessKey 的订阅式访问控制：
- 编辑 `app/config/auth_keys.yaml` 添加用户
- 设置 `AUTH_ENABLED=false` 可禁用认证（仅开发环境）
- Token 有效期通过 `AUTH_SESSION_TTL_MIN` 控制

### ComfyUI 集成
- 配置 `COMFYUI_BASE_URL` 指向 ComfyUI 服务器
- 支持 /prompt 提交、/history 查询、/view 取结果等 API
- 工作流参数通过"参数键路径"动态更新

## 开发指南

### 添加新工作流
1. 在 `app/config/workflows/<workflow_key>/` 创建目录
2. 添加 `metadata.json`（名称、标签、参数路径等）
3. 放置 `workflow.json`（ComfyUI 工作流定义）
4. 在 `app/ui/workflows/` 创建对应的界面文件

### 扩展功能
- 所有 API 端点需要认证（除 /health、/auth/login）
- 使用 Pydantic 模型定义数据结构
- 遵循 DEV/RDY 标签约定控制可见性

## 许可证

[待添加许可证信息]
