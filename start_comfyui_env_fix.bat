@echo off
echo 🚀 启动ComfyUI (环境变量方式)
cd /d "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set TQDM_DISABLE=1
set COMFYUI_DISABLE_LOGGING=1

REM 启动ComfyUI
python main.py --user-directory "C:\Users\<USER>\Documents\ComfyUI\user" --input-directory "C:\Users\<USER>\Documents\ComfyUI\input" --output-directory "C:\Users\<USER>\Documents\ComfyUI\output" --listen 127.0.0.1 --port 8188

pause
