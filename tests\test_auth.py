import pytest
import os
from datetime import datetime, timezone

from app.api.auth import AuthManager
from app.api.schemas import UserInfo

@pytest.fixture
def auth_manager():
    """创建测试用的认证管理器"""
    # 设置测试环境变量
    os.environ["AUTH_ENABLED"] = "true"
    os.environ["AUTH_KEYS_FILE"] = "nonexistent_file.yaml"  # 使用不存在的文件测试默认行为
    os.environ["AUTH_SECRET"] = "test-secret-key"
    os.environ["AUTH_SESSION_TTL_MIN"] = "60"
    
    return AuthManager()

def test_auth_disabled():
    """测试认证禁用时的行为"""
    os.environ["AUTH_ENABLED"] = "false"
    auth_manager = AuthManager()
    
    # 认证禁用时应该返回默认管理员权限
    user = auth_manager.verify_access_key("any_user", "any_key")
    assert user is not None
    assert user.username == "any_user"
    assert "admin" in user.roles

def test_verify_token_with_disabled_auth():
    """测试认证禁用时的令牌验证"""
    os.environ["AUTH_ENABLED"] = "false"
    auth_manager = AuthManager()
    
    user = auth_manager.verify_token("any_token")
    assert user is not None
    assert user.username == "anonymous"
    assert "admin" in user.roles

def test_create_and_verify_token(auth_manager):
    """测试创建和验证令牌"""
    user_info = UserInfo(username="test_user", roles=["user"])
    
    # 创建令牌
    token = auth_manager.create_token(user_info)
    assert isinstance(token, str)
    assert len(token) > 0
    
    # 验证令牌
    verified_user = auth_manager.verify_token(token)
    assert verified_user is not None
    assert verified_user.username == "test_user"
    assert verified_user.roles == ["user"]

def test_verify_invalid_token(auth_manager):
    """测试验证无效令牌"""
    invalid_tokens = [
        "invalid_token",
        "",
        "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
        None
    ]
    
    for token in invalid_tokens:
        user = auth_manager.verify_token(token)
        assert user is None

def test_load_empty_keys(auth_manager):
    """测试加载空密钥配置"""
    # 使用不存在的文件，应该返回空配置
    keys = auth_manager.load_keys()
    assert isinstance(keys, dict)

def test_verify_access_key_no_file(auth_manager):
    """测试在没有密钥文件时验证访问密钥"""
    user = auth_manager.verify_access_key("test_user", "test_key")
    assert user is None

def test_qr_login_methods_return_501(auth_manager):
    """测试扫码登录方法返回 501"""
    from fastapi import HTTPException
    
    methods = [
        auth_manager.init_qr_login,
        lambda: auth_manager.check_qr_status("test_id"),
        lambda: auth_manager.redeem_qr_code("test_id")
    ]
    
    for method in methods:
        with pytest.raises(HTTPException) as exc_info:
            method()
        assert exc_info.value.status_code == 501
        assert "未实现" in exc_info.value.detail

def test_load_whitelist_empty_mode(auth_manager):
    """测试加载白名单（空模式）"""
    auth_manager.whitelist_mode = ""
    whitelist = auth_manager.load_whitelist()
    assert whitelist == {"entries": []}

def test_load_whitelist_online_mode_501(auth_manager):
    """测试在线白名单模式返回 501"""
    from fastapi import HTTPException
    
    auth_manager.whitelist_mode = "online"
    
    with pytest.raises(HTTPException) as exc_info:
        auth_manager.load_whitelist()
    assert exc_info.value.status_code == 501
