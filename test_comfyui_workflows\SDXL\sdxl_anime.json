{"3": {"inputs": {"seed": 12345, "steps": 40, "cfg": 9.0, "sampler_name": "dpmpp_3m_sde", "scheduler": "exponential", "denoise": 1.0, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>"}, "4": {"inputs": {"ckpt_name": "sdxl_turbo.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage"}, "6": {"inputs": {"text": "anime character in dynamic pose", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}, "7": {"inputs": {"text": "static, boring, low quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}}