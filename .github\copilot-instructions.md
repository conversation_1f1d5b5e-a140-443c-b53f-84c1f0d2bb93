# Copilot 指南（FreemanWorkHub）

## 仓库快照（2025-08-29）
- 当前无代码，但已确定技术栈与目标；环境为 Windows，默认 PowerShell 5.1。

## 技术决策与范围
- 单一应用：Streamlit 前端 + FastAPI 后端（同仓库）。仅使用 Python。
- 通过后端与外部 ComfyUI 服务器通信（HTTP/WS）。
- 近期不引入 Docker/数据库；预留用户/权限扩展点与配置持久化。
- 项目与 UI 文案统一使用简体中文。

## 架构一览（建议目录）
- app/ui/：Streamlit 界面
  - Home.py（着陆页：ComfyUI 连接配置 + 工作流 Gallery，支持标签筛选 DEV/RDY）
  - workflows/<workflow_key>.py（单工作流页：可编辑节点参数、输入/输出目录、运行与进度）
  - pipeline_editor.py（复杂工作流编排：串联/条件/批量）
- app/api/：FastAPI 服务
  - main.py（应用入口，/workflows /run /status /outputs 等端点，/docs 提供交互文档）
  - comfy_client.py（使用 httpx 调用 ComfyUI：列举、提交、查询、取回结果）
  - schemas.py（Pydantic 模型：工作流、参数、任务状态、结果）
  - auth.py（认证端点与依赖：/auth/login /auth/me；全局依赖或中间件保护受限端点）
- app/services/：业务编排与批处理（目录遍历、队列策略）
- app/config/：配置管理（.env + config.yaml；优先级：UI > 环境变量 > 文件；本地订阅密钥 auth_keys.yaml）
- tests/：Pytest 用例（最少覆盖 API 客户端与一个示例工作流）

## 关键约定与模式
- 工作流标签：DEV（开发中/普通用户只能看不能使用和编辑）、RDY（可用/展示）。
- 每个工作流目录包含 metadata.json（名称、标签、可编辑参数键路径、默认 I/O 目录）。
- I/O 目录约定：app/data/{inputs,outputs}/<workflow_key>，UI 可覆盖。
- Streamlit 使用 session_state 管理会话；长任务采用队列 + 轮询。

## 与 ComfyUI 集成要点
- 基址来自 COMFYUI_BASE_URL；常用端点：/prompt（提交）、/history|/queue（查询）、/view（取结果）。
- 提交前按“参数键路径”更新工作流 JSON；采用异步提交 + 轮询/回调策略，UI 显示进度与错误。
- 大批量处理走后端任务队列，限制并发与超时，写入 outputs 并回显链接。

## 认证与授权（订阅）
- 所有功能必须登录后使用（即便本地部署）。登录凭据为 用户名 + AccessKey。
- AccessKey 具有有效期（UTC），Key 及其有效期由开发者分发与维护。
- 后端在启动时加载 `app/config/auth_keys.yaml`（不提交真实密钥，提供 `auth_keys.example.yaml`）。
- 认证流程：
  - `POST /auth/login { username, access_key }` → 验证 Key 存在且未过期 → 返回短期会话令牌（签名 Token，含用户与过期时间）。
  - 受限端点（/workflows /run /status /outputs 等）通过全局依赖/中间件校验 `Authorization: Bearer <token>`。
  - 失败场景：未登录/无效/过期 → 401/403；ComfyUI 相关操作一律拒绝。
- Streamlit 前端：
  - 在 `session_state` 存储登录状态与 token；未登录则只显示登录页；登录成功后放行各页面组件。
  - 退出登录清空状态；token 过期后引导重新登录。
- 环境变量（.env）：
  - `AUTH_ENABLED=true|false`（默认 true）
  - `AUTH_KEYS_FILE=app/config/auth_keys.yaml`
  - `AUTH_SECRET=<生成签名的随机字符串>`
  - `AUTH_SESSION_TTL_MIN=1440`（会话令牌有效期，分钟）
  - 生产中仅分发编译后的部署包或只分发过期受控的 keys 文件；开发者更新 keys 文件即可延长/吊销权限。

### 扫码身份 + 白名单（可选方案）
- 场景：通过微信/支付宝扫码确认身份，管理员将扫码成功的身份加入白名单；白名单用户可用，移除或过期后即不可用。
- 推荐实现：发布“签名白名单”（JSON），客户端定时拉取/加载并验签，支持在线与离线两种模式。
  - 在线：`WHITELIST_URL` 指向线上 JSON，后端定时拉取并用 `WHITELIST_PUBKEY_FILE` 验签，缓存 TTL=`WHITELIST_TTL_MIN`。
  - 离线：本地 `WHITELIST_FILE=app/config/whitelist.json`，升级/吊销通过分发新文件。
- 白名单数据模型（最小）：
  - 顶层：`version`、`updated_at`、`entries`、`signature`（对 entries 等字段做 Ed25519/RSA-PSS 签名）。
  - entries[*]：`subject`（如 wechat_unionid/alipay_user_id）、`provider`（wechat|alipay|local）、`username`、`roles`、`expires_utc`、可选 `revoked`。
- 运行时逻辑：登录成功后仍签发短期会话 Token；每次受限请求先校验 Token，再校验 subject 是否在白名单且未过期/未吊销。
- 相关环境变量：
  - `WHITELIST_MODE=online|file`
  - `WHITELIST_URL=<https://auth.example.com/whitelist.json>`
  - `WHITELIST_FILE=app/config/whitelist.json`
  - `WHITELIST_PUBKEY_FILE=app/config/whitelist_pubkey.pem`
  - `WHITELIST_TTL_MIN=60`
  - 扫码本身在 License/Auth Server 完成；本项目仅消费已签发的白名单。

### 本期范围与占位（不实现管理，仅预留接口）
- 本期不实现：密钥发放/续期/吊销后台、白名单签发服务、扫码对接与后台管理 UI。
- 本期必须实现：
  - 基础登录（username + access_key）与受限端点的 Token 校验；未登录拒绝访问。
  - 配置开关与接口占位，返回 501 Not Implemented（当相关能力未启用/未配置时）。
- 预留接口（建议路径/行为）：
  - `POST /auth/qr/init` → 501（当未配置 LICENSE_SERVER_URL 或 AUTH_MODE!=online）
  - `GET /auth/qr/status?login_id=` → 501（同上）
  - `POST /auth/qr/redeem` → 501（同上）
  - `POST /auth/bind` → 501（默认不启用）
  - 白名单加载：当 `WHITELIST_MODE` 未设置时返回“未启用”，否则按模式加载或 501
- 配置开关（占位，默认关闭在线能力）：
  - `AUTH_MODE=keys|whitelist|online`（默认 keys，仅本地 AccessKey 校验）
  - `AUTH_ONLINE_ENFORCE=false`（当为 true 且 LICENSE_SERVER_URL 缺失时，登录直接失败）
  - `LICENSE_SERVER_URL=`（留空表示不启用扫码/在线校验）
- UI 占位：
  - 登录页保留“扫码登录”按钮（禁用态，hover 提示需配置 LICENSE_SERVER_URL）。
  - 设置页保留“白名单来源”下拉（online/file），当未启用时置灰。
- 代码结构占位：
  - `app/api/auth.py` 中保留函数 stub：`load_keys()`, `load_whitelist()`, `verify_token()`, `get_current_user()`；扫码相关 `init_qr_login()`, `check_qr_status()`, `redeem_qr_code()` 返回 501。
  - `app/api/license_server.py` 预留客户端封装（仅方法签名与 501）。
- 错误约定：
  - 未登录/无效 Token → 401；权限不足/白名单不通过 → 403；未启用能力/未配置 → 501。
- 最小测试（占位）：
  - 未携带 Token 访问受限端点返回 401。
  - 调用 /auth/qr/* 在未配置在线模式时返回 501。

## 开发者工作流（PowerShell）
```powershell
python -m venv .venv
.\.venv\Scripts\Activate.ps1
pip install streamlit fastapi "uvicorn[standard]" httpx pydantic python-dotenv PyYAML pytest

# 启动后端（示例端口 8001）
uvicorn app.api.main:app --reload --port 8001
# 启动前端（示例端口 8501）
streamlit run app/ui/Home.py --server.port 8501

# 运行测试
pytest -q
```

## 交付与文档
- 首批脚手架需包含：
  - README（中文快速开始）
  - .env.example（COMFYUI_BASE_URL、API_TIMEOUT、DATA_DIR、AUTH_ENABLED、AUTH_KEYS_FILE、AUTH_SECRET、AUTH_SESSION_TTL_MIN）
  - requirements.txt
  - app/config/auth_keys.example.yaml（示例用户与过期时间，仅示例数据）
  - 一个带 RDY 标签的 demo 工作流与页面
- 变更命令或行为时同步更新 README 与本文件；勿提交敏感信息（.env* 与 auth_keys.yaml 忽略）。

### 白名单附加交付
- app/config/whitelist.example.json（示例结构与少量条目，不含真实签名）
- app/config/whitelist_pubkey.example.pem（示例公钥，占位）

## 给 AI 代理的具体指示
- 按上述目录生成最小可运行版本并自测；优先落地：Gallery、单工作流页、编排器最小版本。
- 引入新约定或端点时，补充到本文件“架构/约定/集成要点”对应段落。
