from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# 认证相关模型
class LoginRequest(BaseModel):
    username: str = Field(..., description="用户名")
    access_key: str = Field(..., description="访问密钥")

class LoginResponse(BaseModel):
    token: str = Field(..., description="JWT 访问令牌")
    expires_at: datetime = Field(..., description="令牌过期时间")
    user: "UserInfo"

class UserInfo(BaseModel):
    username: str
    roles: List[str] = []

# 工作流相关模型
class WorkflowTag(str, Enum):
    DEV = "DEV"  # 开发中，仅管理员可见
    RDY = "RDY"  # 就绪，所有用户可见

class WorkflowMetadata(BaseModel):
    name: str = Field(..., description="工作流显示名称")
    description: Optional[str] = Field(None, description="工作流描述")
    tags: List[WorkflowTag] = Field(default=[WorkflowTag.RDY], description="工作流标签")
    editable_params: Dict[str, Any] = Field(default={}, description="可编辑参数的键路径映射")
    default_input_dir: Optional[str] = Field(None, description="默认输入目录")
    default_output_dir: Optional[str] = Field(None, description="默认输出目录")

class WorkflowInfo(BaseModel):
    key: str = Field(..., description="工作流唯一标识")
    metadata: WorkflowMetadata
    workflow_json: Optional[Dict[str, Any]] = Field(None, description="ComfyUI 工作流定义")
    
    # 文件路径相关信息
    file_path: Optional[str] = Field(None, description="工作流文件的完整路径")
    relative_path: Optional[str] = Field(None, description="相对于工作流根目录的路径")
    parent_dir: Optional[str] = Field(None, description="父目录路径，用于分组显示")

# 任务执行相关模型
class TaskStatus(str, Enum):
    PENDING = "pending"     # 等待执行
    RUNNING = "running"     # 执行中
    COMPLETED = "completed" # 已完成
    FAILED = "failed"       # 执行失败

class RunWorkflowRequest(BaseModel):
    workflow_key: str = Field(..., description="工作流标识")
    parameters: Dict[str, Any] = Field(default={}, description="自定义参数")
    input_dir: Optional[str] = Field(None, description="输入目录路径")
    output_dir: Optional[str] = Field(None, description="输出目录路径")
    workflow_dir: Optional[str] = Field(None, description="ComfyUI工作流目录路径")
    batch_mode: bool = Field(default=False, description="是否批量处理")

class TaskInfo(BaseModel):
    task_id: str = Field(..., description="任务ID")
    workflow_key: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: Optional[Dict[str, Any]] = Field(None, description="执行进度信息")
    error_message: Optional[str] = Field(None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(None, description="执行结果")

# ComfyUI 集成相关模型
class ComfyUIPromptRequest(BaseModel):
    workflow: Dict[str, Any] = Field(..., description="ComfyUI 工作流定义")
    client_id: Optional[str] = Field(None, description="客户端ID")

class ComfyUIPromptResponse(BaseModel):
    prompt_id: str = Field(..., description="ComfyUI 返回的提示ID")

class ComfyUIHistoryItem(BaseModel):
    prompt_id: str
    status: Dict[str, Any]
    outputs: Optional[Dict[str, Any]] = None

# API 响应模型
class ApiResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None
