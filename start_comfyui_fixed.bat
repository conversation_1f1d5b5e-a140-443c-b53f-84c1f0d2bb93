@echo off
echo 🚀 启动ComfyUI (修复进度条问题)
cd /d "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

REM 设置环境变量禁用进度条和日志问题
set TQDM_DISABLE=1
set PYTHONIOENCODING=utf-8
set COMFYUI_DISABLE_LOGGING=1

echo 📋 环境变量设置:
echo   TQDM_DISABLE=1 (禁用tqdm进度条)
echo   PYTHONIOENCODING=utf-8 (修复编码问题)
echo   COMFYUI_DISABLE_LOGGING=1 (禁用问题日志)

REM 启动ComfyUI，添加禁用控制台进度条参数
echo 🔥 启动ComfyUI服务器...
python main.py --user-directory "C:\Users\<USER>\Documents\ComfyUI\user" --input-directory "C:\Users\<USER>\Documents\ComfyUI\input" --output-directory "C:\Users\<USER>\Documents\ComfyUI\output" --listen 127.0.0.1 --port 8188 --disable-auto-launch --disable-console-progress-bar

pause
