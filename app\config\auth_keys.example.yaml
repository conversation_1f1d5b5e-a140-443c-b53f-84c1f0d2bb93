# 访问密钥配置示例
# 实际使用时复制为 auth_keys.yaml 并修改

users:
  admin:
    access_key: "demo-admin-key-12345"
    expires_utc: "2025-12-31T23:59:59Z"
    roles: ["admin"]
    note: "管理员账户 - 可访问 DEV 工作流"
  
  user1:
    access_key: "demo-user-key-67890"
    expires_utc: "2025-06-30T23:59:59Z"
    roles: ["user"]
    note: "普通用户 - 仅可访问 RDY 工作流"

# 字段说明：
# - access_key: 登录时使用的密钥
# - expires_utc: UTC 时间，过期后无法登录
# - roles: 角色列表，影响可访问的功能
# - note: 备注信息（可选）
