#!/usr/bin/env python3
"""
Debug External Workflows
Investigates parameter contamination in external ComfyUI workflows
"""
import json
import sys
import asyncio
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service
from app.api.comfy_client import comfy_client
from app.ui.workflows.workflow_utils import analyze_workflow_nodes, is_model_loader_node

async def debug_external_workflows():
    """Debug external workflow handling"""
    
    print("🔍 Debugging External Workflow Parameter Contamination")
    print("=" * 60)
    
    try:
        # Get all workflows (including external ones)
        all_workflows = await workflow_service.list_workflows_async()
        
        print(f"📊 Found {len(all_workflows)} total workflows")
        
        # Categorize workflows
        local_workflows = []
        external_workflows = []
        
        for workflow in all_workflows:
            if "app/config/workflows" in workflow.file_path:
                local_workflows.append(workflow)
            else:
                external_workflows.append(workflow)
        
        print(f"   Local workflows: {len(local_workflows)}")
        print(f"   External workflows: {len(external_workflows)}")
        
        # Focus on external workflows that might have the problematic nodes
        for workflow in external_workflows:
            print(f"\n🔧 Analyzing external workflow: {workflow.key}")
            print(f"   File: {workflow.file_path}")
            
            try:
                workflow_json = workflow.workflow_json
                if not workflow_json:
                    print(f"   ❌ No workflow JSON found")
                    continue
                
                # Look for the problematic node types
                problematic_nodes = []
                for node_id, node_data in workflow_json.items():
                    if not isinstance(node_data, dict):
                        continue
                    
                    class_type = node_data.get("class_type", "")
                    if class_type in ["DualCLIPLoader", "VAELoader", "UNETLoader", "LoraLoaderModelOnly", "UpscaleModelLoader"]:
                        problematic_nodes.append((node_id, class_type, node_data.get("inputs", {})))
                
                if problematic_nodes:
                    print(f"   🚨 Found {len(problematic_nodes)} model loader nodes:")
                    for node_id, class_type, inputs in problematic_nodes:
                        print(f"     Node {node_id} ({class_type}):")
                        for input_name, input_value in inputs.items():
                            if not isinstance(input_value, list):  # Skip connections
                                print(f"       {input_name}: {input_value}")
                    
                    # Check if these nodes are being classified as editable
                    analysis = analyze_workflow_nodes(workflow_json)
                    editable_nodes = analysis.get("editable_nodes", [])
                    
                    contaminated_editables = []
                    for editable_node in editable_nodes:
                        node_id = editable_node["id"]
                        if any(node_id == pn[0] for pn in problematic_nodes):
                            contaminated_editables.append(editable_node)
                    
                    if contaminated_editables:
                        print(f"   🚨 CRITICAL: {len(contaminated_editables)} model loader nodes incorrectly classified as editable!")
                        for node in contaminated_editables:
                            print(f"     ❌ Node {node['id']} ({node['class_type']}) should NOT be editable")
                            print(f"        Title: {node['title']}")
                            for param in node["editable_params"]:
                                print(f"        - {param['path']}: {param['current_value']}")
                    else:
                        print(f"   ✅ Model loader nodes correctly excluded from editing")
                
                else:
                    print(f"   ℹ️  No model loader nodes found")
            
            except Exception as e:
                print(f"   ❌ Failed to analyze workflow: {e}")
        
        # Test the node classification functions directly
        print(f"\n🧪 Testing Node Classification Functions:")
        test_node_types = ["DualCLIPLoader", "VAELoader", "UNETLoader", "LoraLoaderModelOnly", "UpscaleModelLoader", "CheckpointLoaderSimple"]
        
        for node_type in test_node_types:
            is_model = is_model_loader_node(node_type)
            print(f"   {node_type}: Model Loader = {'✅' if is_model else '❌'}")
            
            if not is_model:
                print(f"     🚨 CRITICAL: {node_type} should be classified as model loader!")
    
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

async def test_parameter_isolation():
    """Test that only intended parameters are modified"""
    
    print(f"\n🧪 Testing Parameter Isolation")
    print("=" * 40)
    
    # Create a test workflow with model loaders
    test_workflow = {
        "1": {
            "inputs": {
                "text": "test prompt",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Text Prompt"}
        },
        "2": {
            "inputs": {
                "clip_name1": "clip_l.safetensors",
                "clip_name2": "t5xxl_fp16.safetensors"
            },
            "class_type": "DualCLIPLoader",
            "_meta": {"title": "CLIP Loader"}
        },
        "3": {
            "inputs": {
                "unet_name": "flux1-dev.safetensors"
            },
            "class_type": "UNETLoader", 
            "_meta": {"title": "UNET Loader"}
        },
        "4": {
            "inputs": {
                "steps": 20,
                "cfg": 7.0
            },
            "class_type": "KSampler",
            "_meta": {"title": "Sampler"}
        }
    }
    
    print(f"📊 Test workflow created with {len(test_workflow)} nodes")
    
    # Test parameter extraction
    analysis = analyze_workflow_nodes(test_workflow)
    editable_nodes = analysis.get("editable_nodes", [])
    
    print(f"🔧 Editable nodes found: {len(editable_nodes)}")
    for node in editable_nodes:
        print(f"   Node {node['id']} ({node['class_type']}): {node['title']}")
    
    # Check if model loaders are incorrectly included
    model_loader_types = ["DualCLIPLoader", "UNETLoader"]
    contaminated = [node for node in editable_nodes if node["class_type"] in model_loader_types]
    
    if contaminated:
        print(f"🚨 CRITICAL BUG: {len(contaminated)} model loader nodes incorrectly classified as editable!")
        for node in contaminated:
            print(f"   ❌ {node['class_type']} (Node {node['id']}) should NOT be editable")
    else:
        print(f"✅ Model loader nodes correctly excluded from editing")
    
    # Test parameter update
    test_parameters = {"1.inputs.text": "modified prompt", "4.inputs.steps": 25}
    
    from app.api.comfy_client import ComfyUIClient
    client = ComfyUIClient()
    updated_workflow = client.update_workflow_parameters(test_workflow, test_parameters)
    
    # Check for unintended changes
    print(f"\n🔍 Checking for Unintended Changes:")
    unintended_changes = []
    
    for node_id, original_node in test_workflow.items():
        updated_node = updated_workflow.get(node_id, {})
        original_inputs = original_node.get("inputs", {})
        updated_inputs = updated_node.get("inputs", {})
        
        for input_name, original_value in original_inputs.items():
            updated_value = updated_inputs.get(input_name)
            param_path = f"{node_id}.inputs.{input_name}"
            
            if updated_value != original_value and param_path not in test_parameters:
                unintended_changes.append({
                    "node_id": node_id,
                    "class_type": original_node.get("class_type"),
                    "input_name": input_name,
                    "original": original_value,
                    "updated": updated_value,
                    "path": param_path
                })
    
    if unintended_changes:
        print(f"🚨 CRITICAL: {len(unintended_changes)} unintended changes detected!")
        for change in unintended_changes:
            print(f"   ❌ Node {change['node_id']} ({change['class_type']}):")
            print(f"      {change['input_name']}: {change['original']} → {change['updated']}")
    else:
        print(f"✅ No unintended changes detected")

if __name__ == "__main__":
    asyncio.run(debug_external_workflows())
    asyncio.run(test_parameter_isolation())
