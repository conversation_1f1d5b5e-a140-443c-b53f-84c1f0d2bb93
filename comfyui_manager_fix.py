#!/usr/bin/env python3
"""
ComfyUI-Manager日志问题修复脚本
解决OSError: [<PERSON>rrno 22] Invalid argument问题
"""

import os
import shutil
from pathlib import Path

def backup_and_fix_manager_script():
    """备份并修复ComfyUI-Manager脚本"""
    manager_script = Path("C:/Users/<USER>/AppData/Local/Programs/@comfyorgcomfyui-electron/resources/ComfyUI/custom_nodes/ComfyUI-Manager/prestartup_script.py")
    
    if not manager_script.exists():
        print(f"❌ 未找到ComfyUI-Manager脚本: {manager_script}")
        return False
    
    # 创建备份
    backup_path = manager_script.with_suffix('.py.backup')
    if not backup_path.exists():
        shutil.copy2(manager_script, backup_path)
        print(f"✅ 创建备份: {backup_path}")
    
    try:
        # 读取原文件
        with open(manager_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修复问题行
        lines = content.split('\n')
        modified = False
        
        for i, line in enumerate(lines):
            # 查找flush调用
            if 'original_stderr.flush()' in line and not line.strip().startswith('#'):
                lines[i] = f"        # {line.strip()}  # 临时注释以修复OSError"
                modified = True
                print(f"✅ 修复第{i+1}行: {line.strip()}")
        
        if modified:
            # 写入修复后的文件
            with open(manager_script, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            print(f"✅ 修复完成: {manager_script}")
            return True
        else:
            print("❓ 未找到需要修复的代码行")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_comfyui_startup_script():
    """创建ComfyUI启动脚本，禁用有问题的日志"""
    script_content = '''@echo off
echo 🚀 启动ComfyUI (禁用Manager日志)
cd /d "C:\\Users\\<USER>\\AppData\\Local\\Programs\\@comfyorgcomfyui-electron\\resources\\ComfyUI"

REM 设置环境变量禁用tqdm
set TQDM_DISABLE=1

REM 启动ComfyUI
python main.py --user-directory "C:\\Users\\<USER>\\Documents\\ComfyUI\\user" --input-directory "C:\\Users\\<USER>\\Documents\\ComfyUI\\input" --output-directory "C:\\Users\\<USER>\\Documents\\ComfyUI\\output" --listen 127.0.0.1 --port 8188 --disable-auto-launch

pause
'''
    
    script_path = Path("start_comfyui_fixed.bat")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 创建ComfyUI启动脚本: {script_path}")
    print("  使用此脚本启动ComfyUI可以避免日志问题")

def create_alternative_workflow_test():
    """创建替代的工作流测试"""
    print("\n🧪 创建替代测试方案...")
    
    # 方案1: 使用不同的采样器
    print("  方案1: 修改采样器为euler (更稳定)")
    print("  方案2: 减少采样步数到20步")
    print("  方案3: 使用不同的调度器normal")
    
    alternative_params = {
        "sampler_name": "euler",
        "scheduler": "normal", 
        "steps": 20,
        "cfg": 7.0
    }
    
    print(f"  建议参数: {alternative_params}")

def main():
    """主修复流程"""
    print("=" * 60)
    print("🔧 ComfyUI-Manager日志问题修复")
    print("=" * 60)
    
    print("📋 问题分析:")
    print("  - 工作流JSON格式正确 ✅")
    print("  - 成功提交并获得prompt_id ✅")
    print("  - 但在KSampler执行时出现OSError ❌")
    print("  - 根因: ComfyUI-Manager日志重定向问题 🎯")
    
    print("\n🛠️ 修复方案:")
    
    # 方案1: 修复Manager脚本
    print("\n1️⃣ 修复ComfyUI-Manager脚本")
    if backup_and_fix_manager_script():
        print("  ✅ 修复完成，请重启ComfyUI测试")
    else:
        print("  ❌ 自动修复失败，请手动处理")
    
    # 方案2: 创建启动脚本
    print("\n2️⃣ 创建安全启动脚本")
    create_comfyui_startup_script()
    
    # 方案3: 替代参数
    print("\n3️⃣ 替代参数建议")
    create_alternative_workflow_test()
    
    print("\n📝 建议执行顺序:")
    print("  1. 关闭当前ComfyUI")
    print("  2. 使用 start_comfyui_fixed.bat 重启")
    print("  3. 或者手动重启ComfyUI并测试")
    print("  4. 如果仍有问题，尝试修改采样器参数")
    
    print("\n🎯 关键提示:")
    print("  你的API格式优化是成功的！")
    print("  现在的问题是ComfyUI环境问题，不是代码问题")

if __name__ == "__main__":
    main()
