import pytest
import tempfile
import os
import json
from pathlib import Path

from app.services.workflow_service import WorkflowService
from app.api.schemas import WorkflowMetadata, WorkflowTag

@pytest.fixture
def temp_workflows_dir():
    """创建临时工作流目录"""
    temp_dir = tempfile.mkdtemp()
    original_dir = WorkflowService().workflows_dir
    
    # 临时替换工作流目录
    service = WorkflowService()
    service.workflows_dir = Path(temp_dir)
    
    yield service
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)

def test_create_workflow(temp_workflows_dir):
    """测试创建工作流"""
    service = temp_workflows_dir
    
    metadata = WorkflowMetadata(
        name="测试工作流",
        description="这是一个测试工作流",
        tags=[WorkflowTag.RDY],
        editable_params={"test_param": "path.to.param"}
    )
    
    workflow = service.create_workflow("test_workflow", metadata)
    
    assert workflow.key == "test_workflow"
    assert workflow.metadata.name == "测试工作流"
    assert WorkflowTag.RDY in workflow.metadata.tags

def test_list_workflows(temp_workflows_dir):
    """测试列出工作流"""
    service = temp_workflows_dir
    
    # 创建测试工作流
    metadata = WorkflowMetadata(
        name="测试工作流",
        tags=[WorkflowTag.RDY]
    )
    service.create_workflow("test_workflow", metadata)
    
    workflows = service.list_workflows()
    assert len(workflows) == 1
    assert workflows[0].key == "test_workflow"

def test_get_workflow(temp_workflows_dir):
    """测试获取工作流详情"""
    service = temp_workflows_dir
    
    # 创建测试工作流
    metadata = WorkflowMetadata(
        name="测试工作流",
        tags=[WorkflowTag.DEV]
    )
    workflow_json = {"test": "data"}
    service.create_workflow("test_workflow", metadata, workflow_json)
    
    # 获取工作流
    workflow = service.get_workflow("test_workflow")
    assert workflow is not None
    assert workflow.key == "test_workflow"
    assert workflow.workflow_json == {"test": "data"}

def test_get_nonexistent_workflow(temp_workflows_dir):
    """测试获取不存在的工作流"""
    service = temp_workflows_dir
    
    workflow = service.get_workflow("nonexistent")
    assert workflow is None

def test_delete_workflow(temp_workflows_dir):
    """测试删除工作流"""
    service = temp_workflows_dir
    
    # 创建测试工作流
    metadata = WorkflowMetadata(name="测试工作流")
    service.create_workflow("test_workflow", metadata)
    
    # 确认工作流存在
    assert service.get_workflow("test_workflow") is not None
    
    # 删除工作流
    result = service.delete_workflow("test_workflow")
    assert result is True
    
    # 确认工作流已删除
    assert service.get_workflow("test_workflow") is None

def test_update_workflow(temp_workflows_dir):
    """测试更新工作流"""
    service = temp_workflows_dir
    
    # 创建初始工作流
    metadata = WorkflowMetadata(name="原始名称")
    service.create_workflow("test_workflow", metadata)
    
    # 更新工作流
    new_metadata = WorkflowMetadata(
        name="更新后名称",
        description="更新后描述"
    )
    
    updated = service.update_workflow("test_workflow", new_metadata)
    
    assert updated is not None
    assert updated.metadata.name == "更新后名称"
    assert updated.metadata.description == "更新后描述"
