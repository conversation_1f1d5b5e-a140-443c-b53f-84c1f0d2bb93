@echo off
echo Starting ComfyUI with progress bar fix...
cd /d "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

set TQDM_DISABLE=1
set PYTHONIOENCODING=utf-8

python main.py --user-directory "C:\Users\<USER>\Documents\ComfyUI\user" --input-directory "C:\Users\<USER>\Documents\ComfyUI\input" --output-directory "C:\Users\<USER>\Documents\ComfyUI\output" --listen 127.0.0.1 --port 8188 --disable-auto-launch --disable-console-progress-bar

pause
