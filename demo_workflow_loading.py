#!/usr/bin/env python3
"""
FreemanWorkHub 演示脚本
展示如何使用新的ComfyUI工作流目录功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.workflow_service import WorkflowService
from app.api.comfy_client import ComfyUIClient

async def demo_workflow_loading():
    """演示工作流加载功能"""
    print("🎨 FreemanWorkHub 工作流加载演示")
    print("=" * 50)

    # 创建服务实例
    workflow_service = WorkflowService()
    comfy_client = ComfyUIClient()

    # 设置测试工作流目录
    test_dir = str(project_root / "test_workflows")
    print(f"📁 设置工作流目录: {test_dir}")

    workflow_service.set_comfyui_workflow_dir(test_dir)

    # 加载工作流
    print("🔄 正在加载工作流...")
    workflows = await workflow_service.list_workflows()

    print(f"✅ 成功加载 {len(workflows)} 个工作流:")
    print()

    for i, workflow in enumerate(workflows, 1):
        print(f"{i}. 📋 {workflow.metadata.name}")
        print(f"   🔑 键名: {workflow.key}")
        print(f"   📝 描述: {workflow.metadata.description}")
        print(f"   🏷️  标签: {', '.join(tag.value for tag in workflow.metadata.tags)}")

        if workflow.metadata.editable_params:
            print(f"   ⚙️  可编辑参数 ({len(workflow.metadata.editable_params)} 个):")
            for param_name, param_path in workflow.metadata.editable_params.items():
                print(f"      - {param_name}: {param_path}")
        else:
            print("   ⚙️  可编辑参数: 无")

        print(f"   📥 输入目录: {workflow.metadata.default_input_dir}")
        print(f"   📤 输出目录: {workflow.metadata.default_output_dir}")
        print()

    # 演示获取特定工作流
    if workflows:
        first_workflow = workflows[0]
        print(f"🔍 获取工作流详情: {first_workflow.key}")
        detail_workflow = await workflow_service.get_workflow_async(first_workflow.key)

        if detail_workflow:
            print("✅ 成功获取工作流详情")
            print(f"   工作流JSON节点数: {len(detail_workflow.workflow_json or {})}")
        else:
            print("❌ 获取工作流详情失败")
    print("\n🎉 演示完成！")

if __name__ == "__main__":
    asyncio.run(demo_workflow_loading())
