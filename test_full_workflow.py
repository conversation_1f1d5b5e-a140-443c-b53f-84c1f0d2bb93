#!/usr/bin/env python3
"""
完整的工作流测试脚本
测试从前端到ComfyUI的完整流程
"""

import requests
import json
import time
import os
from pathlib import Path

# 配置
FASTAPI_BASE_URL = "http://localhost:18001"
COMFYUI_BASE_URL = "http://localhost:8188"

def test_fastapi_connection():
    """测试FastAPI后端连接"""
    print("🔍 测试FastAPI后端连接...")
    
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI后端运行正常")
            return True
        else:
            print(f"❌ FastAPI后端响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到FastAPI后端: {e}")
        print("💡 请确保后端已启动: uvicorn app.api.main:app --reload --port 18001")
        return False

def test_comfyui_connection():
    """测试ComfyUI连接"""
    print("🔍 测试ComfyUI连接...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/system_stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print("✅ ComfyUI服务器运行正常")
            print(f"  版本: {stats.get('system', {}).get('comfyui_version', '未知')}")
            return True
        else:
            print(f"❌ ComfyUI服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到ComfyUI服务器: {e}")
        print("💡 请确保ComfyUI已启动并使用修复后的启动脚本")
        return False

def get_test_workflow():
    """获取测试工作流"""
    print("📋 准备测试工作流...")
    
    # 使用简化的测试工作流
    test_workflow_key = "RDY_LOGO-Gen-API"
    
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/workflows/{test_workflow_key}")
        if response.status_code == 200:
            workflow_data = response.json()
            print(f"✅ 成功获取工作流: {test_workflow_key}")
            return workflow_data.get("workflow")
        else:
            print(f"❌ 获取工作流失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取工作流时出错: {e}")
        return None

def submit_test_workflow(workflow):
    """提交测试工作流"""
    print("🚀 提交测试工作流...")
    
    # 准备运行参数
    run_params = {
        "workflow_key": "RDY_LOGO-Gen-API",
        "parameters": {
            "3.inputs.steps": 5,  # 减少步数加快测试
            "3.inputs.sampler_name": "euler",  # 使用稳定的采样器
            "3.inputs.scheduler": "normal",    # 使用普通调度器
            "6.inputs.text": "test logo, simple design"  # 简化提示词
        },
        "input_dir": "",
        "output_dir": "C:\\Users\\<USER>\\Documents\\ComfyUI\\output",
        "workflow_dir": "C:\\Users\\<USER>\\Documents\\ComfyUI\\user\\default\\workflows",
        "batch_mode": False
    }
    
    try:
        response = requests.post(
            f"{FASTAPI_BASE_URL}/run",
            json=run_params,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务提交成功: {task_id}")
            return task_id
        else:
            print(f"❌ 任务提交失败: {response.status_code}")
            print(f"  响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 提交任务时出错: {e}")
        return None

def monitor_task(task_id, max_wait_time=120):
    """监控任务执行状态"""
    print(f"⏳ 监控任务执行 (最多等待{max_wait_time}秒)...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{FASTAPI_BASE_URL}/tasks/{task_id}", timeout=5)
            if response.status_code == 200:
                task_data = response.json()
                status = task_data.get("status", "unknown")
                
                print(f"📊 任务状态: {status}")
                
                if status == "completed":
                    print("🎉 任务执行完成！")
                    result = task_data.get("result", {})
                    if result.get("outputs"):
                        print(f"📁 生成了 {len(result['outputs'])} 个输出")
                    return True
                elif status == "failed":
                    print("❌ 任务执行失败")
                    error = task_data.get("error", "未知错误")
                    print(f"  错误信息: {error}")
                    return False
                elif status == "running":
                    progress = task_data.get("progress", {})
                    if progress:
                        print(f"📈 执行进度: {progress}")
            
            time.sleep(3)
            print(".", end="", flush=True)
            
        except Exception as e:
            print(f"\n❌ 监控过程出错: {e}")
            break
    
    print(f"\n⏰ 监控超时")
    return False

def main():
    """主测试流程"""
    print("=" * 60)
    print("🧪 完整工作流测试")
    print("=" * 60)
    
    print("📋 测试步骤:")
    print("  1. 检查FastAPI后端连接")
    print("  2. 检查ComfyUI服务器连接") 
    print("  3. 获取测试工作流")
    print("  4. 提交工作流执行")
    print("  5. 监控执行状态")
    print()
    
    # 1. 检查FastAPI连接
    if not test_fastapi_connection():
        return
    
    # 2. 检查ComfyUI连接
    if not test_comfyui_connection():
        return
    
    # 3. 获取工作流
    workflow = get_test_workflow()
    if not workflow:
        return
    
    # 4. 提交工作流
    task_id = submit_test_workflow(workflow)
    if not task_id:
        return
    
    # 5. 监控执行
    success = monitor_task(task_id)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 完整测试成功！")
        print("✅ 端口占用问题已澄清：不存在端口冲突")
        print("✅ 进度条问题已修复：ComfyUI可以正常执行")
        print("✅ API通信正常：FastAPI ↔ ComfyUI 工作正常")
    else:
        print("❌ 测试失败")
        print("💡 如果仍有问题，请检查:")
        print("  1. 是否使用了修复后的ComfyUI启动脚本")
        print("  2. 模型文件是否存在")
        print("  3. 输出目录是否有写入权限")
    print("=" * 60)

if __name__ == "__main__":
    main()
