["test_recursive.py::test_recursive_search", "tests/test_api.py::test_demo_workflow_exists", "tests/test_api.py::test_health_check", "tests/test_api.py::test_login_endpoint_disabled_auth", "tests/test_api.py::test_outputs_endpoint", "tests/test_api.py::test_qr_login_endpoints_return_501", "tests/test_api.py::test_run_workflow_missing_workflow", "tests/test_api.py::test_task_status_nonexistent", "tests/test_api.py::test_unauthorized_access_with_auth_enabled", "tests/test_api.py::test_workflows_endpoint", "tests/test_auth.py::test_auth_disabled", "tests/test_auth.py::test_create_and_verify_token", "tests/test_auth.py::test_load_empty_keys", "tests/test_auth.py::test_load_whitelist_empty_mode", "tests/test_auth.py::test_load_whitelist_online_mode_501", "tests/test_auth.py::test_qr_login_methods_return_501", "tests/test_auth.py::test_verify_access_key_no_file", "tests/test_auth.py::test_verify_invalid_token", "tests/test_auth.py::test_verify_token_with_disabled_auth", "tests/test_workflow_service.py::test_create_workflow", "tests/test_workflow_service.py::test_delete_workflow", "tests/test_workflow_service.py::test_get_nonexistent_workflow", "tests/test_workflow_service.py::test_get_workflow", "tests/test_workflow_service.py::test_list_workflows", "tests/test_workflow_service.py::test_update_workflow"]