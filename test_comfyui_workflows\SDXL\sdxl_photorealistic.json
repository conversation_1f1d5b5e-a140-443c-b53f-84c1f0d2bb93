{"3": {"inputs": {"seed": 12345, "steps": 35, "cfg": 8.5, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 1.0, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>"}, "4": {"inputs": {"ckpt_name": "stable-diffusion-xl-base.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "5": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage"}, "6": {"inputs": {"text": "a photorealistic portrait of a person", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}, "7": {"inputs": {"text": "unrealistic, cartoon, low quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode"}}