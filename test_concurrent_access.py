#!/usr/bin/env python3
"""
Concurrent Access Test
Tests multiple simultaneous save operations to verify file locking and corruption prevention
"""
import asyncio
import json
import sys
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import random

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service

async def test_concurrent_saves():
    """Test concurrent save operations"""
    
    print("🧪 Testing Concurrent Save Operations")
    print("=" * 50)
    
    # Test workflow
    workflow_key = "demo_text_generation"
    
    try:
        # Get the workflow
        workflow = await workflow_service.get_workflow_async(workflow_key)
        if not workflow:
            print(f"❌ Workflow {workflow_key} not found")
            return
        
        print(f"✅ Testing workflow: {workflow.metadata.name}")
        
        # Create multiple different parameter sets
        test_scenarios = []
        for i in range(5):
            test_parameters = {
                "3.inputs.text": f"并发测试提示词 #{i+1} - {random.randint(1000, 9999)}",
                "7.inputs.text": f"并发测试负面提示词 #{i+1}",
                "10.inputs.steps": 20 + i,
                "10.inputs.cfg": 7.0 + i * 0.5
            }
            test_scenarios.append((i+1, test_parameters))
        
        print(f"📊 准备执行 {len(test_scenarios)} 个并发保存操作")
        
        # Execute concurrent saves
        async def save_workflow_test(scenario_id, parameters):
            try:
                print(f"🚀 启动保存操作 #{scenario_id}")
                start_time = time.time()
                
                result = await workflow_service.save_workflow(
                    workflow_key,
                    parameters=parameters,
                    make_backup=True
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"✅ 保存操作 #{scenario_id} 完成 ({duration:.2f}s)")
                
                # Verify the save
                verifications = result.get('verifications', {})
                failed_verifications = [k for k, v in verifications.items() if not v.get('ok', True)]
                
                if failed_verifications:
                    print(f"⚠️ 保存操作 #{scenario_id} 部分失败: {failed_verifications}")
                    return False
                else:
                    print(f"✅ 保存操作 #{scenario_id} 验证通过")
                    return True
                    
            except Exception as e:
                print(f"❌ 保存操作 #{scenario_id} 失败: {e}")
                return False
        
        # Run concurrent saves
        print(f"\n🔄 开始并发保存测试...")
        start_time = time.time()
        
        tasks = [save_workflow_test(scenario_id, params) for scenario_id, params in test_scenarios]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Analyze results
        successful_saves = sum(1 for r in results if r is True)
        failed_saves = len(results) - successful_saves
        
        print(f"\n📊 并发测试结果:")
        print(f"   总耗时: {total_duration:.2f}s")
        print(f"   成功保存: {successful_saves}/{len(test_scenarios)}")
        print(f"   失败保存: {failed_saves}/{len(test_scenarios)}")
        
        if failed_saves == 0:
            print(f"🎉 所有并发保存操作都成功完成！")
        else:
            print(f"⚠️ 有 {failed_saves} 个保存操作失败")
        
        # Check final file integrity
        print(f"\n🔍 检查最终文件完整性...")
        final_workflow = await workflow_service.get_workflow_async(workflow_key)
        if final_workflow and final_workflow.workflow_json:
            node_count = len(final_workflow.workflow_json)
            print(f"✅ 工作流文件完整性正常 ({node_count} 个节点)")
        else:
            print(f"❌ 工作流文件损坏或丢失")
        
        # Check backup files
        workflow_file = Path(workflow.file_path)
        backup_files = list(workflow_file.parent.glob(f"{workflow_file.stem}.bak.*"))
        print(f"📁 备份文件数量: {len(backup_files)}")
        
        if len(backup_files) > 10:
            print(f"⚠️ 备份文件过多，可能需要清理")
        
    except Exception as e:
        print(f"❌ 并发测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_file_recovery():
    """Test file recovery mechanism"""
    
    print(f"\n🧪 Testing File Recovery Mechanism")
    print("=" * 50)
    
    workflow_key = "demo_text_generation"
    
    try:
        workflow = await workflow_service.get_workflow_async(workflow_key)
        if not workflow:
            print(f"❌ Workflow {workflow_key} not found")
            return
        
        workflow_file = Path(workflow.file_path)
        
        # Create a backup of the current file
        backup_content = workflow_file.read_text(encoding='utf-8')
        
        print(f"📁 测试文件: {workflow_file}")
        
        # Simulate file corruption
        print(f"🔧 模拟文件损坏...")
        workflow_file.write_text("", encoding='utf-8')  # Empty the file
        
        # Try to load the workflow (should trigger recovery)
        print(f"🔄 尝试加载损坏的工作流...")
        recovered_workflow = await workflow_service.get_workflow_async(workflow_key)
        
        if recovered_workflow and recovered_workflow.workflow_json:
            print(f"✅ 文件恢复成功！")
            node_count = len(recovered_workflow.workflow_json)
            print(f"   恢复的节点数: {node_count}")
        else:
            print(f"❌ 文件恢复失败")
            # Restore manually
            workflow_file.write_text(backup_content, encoding='utf-8')
            print(f"🔄 手动恢复原始文件")
        
    except Exception as e:
        print(f"❌ 恢复测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function"""
    
    print("🚀 Starting Comprehensive Workflow System Tests")
    print("=" * 60)
    
    # Test 1: Concurrent saves
    await test_concurrent_saves()
    
    # Wait a bit between tests
    await asyncio.sleep(2)
    
    # Test 2: File recovery
    await test_file_recovery()
    
    print(f"\n🏁 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
