#!/usr/bin/env python3
"""
Debug script to test workflow parameter path generation and mapping
"""
import json
import sys
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from ui.workflows.workflow_utils import analyze_workflow_nodes
from api.comfy_client import ComfyUIClient

def test_parameter_paths():
    """Test parameter path generation for a sample workflow"""
    
    # Load a sample workflow
    workflow_path = Path("app/config/workflows/demo_text_generation/workflow.json")
    
    if not workflow_path.exists():
        print(f"❌ Workflow file not found: {workflow_path}")
        return
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow_json = json.load(f)
        
        print("✅ Loaded workflow JSON successfully")
        print(f"📊 Workflow has {len(workflow_json)} nodes")
        
        # Analyze the workflow to extract editable parameters
        analysis = analyze_workflow_nodes(workflow_json)
        editable_nodes = analysis.get("editable_nodes", [])
        
        print(f"🔧 Found {len(editable_nodes)} editable nodes")
        
        # Test parameter path generation
        test_parameters = {}
        for node in editable_nodes:
            node_id = node["id"]
            print(f"\n📝 Node {node_id} ({node['title']}):")
            
            for param in node["editable_params"]:
                param_path = param["path"]
                current_value = param["current_value"]
                print(f"  - Path: {param_path}")
                print(f"    Current: {current_value}")
                print(f"    Type: {param.get('type', 'unknown')}")
                
                # Create test parameter update
                test_parameters[param_path] = f"TEST_VALUE_{node_id}"
        
        print(f"\n🧪 Test parameters to apply: {test_parameters}")
        
        # Test the update_workflow_parameters function
        client = ComfyUIClient()
        try:
            updated_workflow = client.update_workflow_parameters(workflow_json, test_parameters)
            print("✅ Parameter update function executed successfully")
            
            # Verify the updates were applied
            print("\n🔍 Verification:")
            for param_path, expected_value in test_parameters.items():
                parts = param_path.split('.')
                if len(parts) >= 3:
                    node_id = parts[0]
                    if node_id in updated_workflow:
                        node_data = updated_workflow[node_id]
                        if parts[1] == "inputs" and parts[2] in node_data.get("inputs", {}):
                            actual_value = node_data["inputs"][parts[2]]
                            status = "✅" if actual_value == expected_value else "❌"
                            print(f"  {status} {param_path}: {actual_value} (expected: {expected_value})")
                        else:
                            print(f"  ❌ {param_path}: Path not found in updated workflow")
                    else:
                        print(f"  ❌ {param_path}: Node {node_id} not found in updated workflow")
        
        except Exception as e:
            print(f"❌ Parameter update failed: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ Failed to load workflow: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_parameter_paths()
