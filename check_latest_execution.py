#!/usr/bin/env python3
"""
检查最新工作流执行状态
"""

import requests
import time

COMFYUI_BASE_URL = "http://localhost:8188"
LATEST_PROMPT_ID = "edcce123-5304-4e16-982f-a0e393d5aa5b"

def check_execution_status():
    """检查最新执行状态"""
    print(f"🔍 检查Prompt {LATEST_PROMPT_ID} 的执行状态...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/history/{LATEST_PROMPT_ID}", timeout=5)
        if response.status_code == 200:
            history = response.json()
            if history:
                for key, data in history.items():
                    status = data.get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)
                    
                    print(f"✅ 执行状态: {status_str}")
                    print(f"✅ 是否完成: {completed}")
                    
                    if status_str == "success" and completed:
                        outputs = data.get("outputs", {})
                        print(f"🎉 工作流执行成功！生成了 {len(outputs)} 个输出")
                        for node_id, output in outputs.items():
                            if "images" in output:
                                images = output["images"]
                                print(f"  节点 {node_id}: 生成 {len(images)} 张图像")
                                for img in images:
                                    filename = img.get("filename", "unknown")
                                    print(f"    - {filename}")
                        return True
                    elif status_str == "error":
                        messages = status.get("messages", [])
                        for msg in messages:
                            if msg[0] == "execution_error":
                                error_info = msg[1]
                                print(f"❌ 执行错误: {error_info.get('exception_message', 'Unknown')}")
                        return False
                    else:
                        print("⏳ 还在执行中...")
                        return None
            else:
                print("❌ 未找到执行历史")
                return False
    except Exception as e:
        print(f"❌ 检查执行状态失败: {e}")
        return None

def check_queue():
    """检查队列状态"""
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/queue", timeout=5)
        if response.status_code == 200:
            queue_data = response.json()
            queue_running = queue_data.get("queue_running", [])
            queue_pending = queue_data.get("queue_pending", [])
            
            print(f"📋 队列状态:")
            print(f"  正在执行: {len(queue_running)} 个任务")
            print(f"  等待执行: {len(queue_pending)} 个任务")
            
            return len(queue_running) > 0 or len(queue_pending) > 0
    except Exception as e:
        print(f"❌ 检查队列失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔍 检查最新工作流执行状态")
    print("=" * 50)
    
    # 检查队列
    has_tasks = check_queue()
    
    # 检查执行状态
    for i in range(3):  # 最多检查3次
        result = check_execution_status()
        
        if result is True:
            print("\n🎉 工作流执行成功！问题已解决！")
            break
        elif result is False:
            print("\n❌ 工作流执行失败，需要进一步调试")
            break
        else:
            print(f"\n⏳ 第{i+1}次检查：还在执行中，等待5秒...")
            time.sleep(5)
    
    print("\n" + "=" * 50)
