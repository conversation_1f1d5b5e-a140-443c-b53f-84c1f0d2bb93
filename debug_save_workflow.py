#!/usr/bin/env python3
"""
Debug script to test the complete workflow save operation
"""
import json
import sys
import os
import asyncio
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service
from app.api.comfy_client import ComfyUIClient

async def test_save_workflow():
    """Test the complete workflow save operation"""
    
    # Test with the recovered workflow
    workflow_key = "RDY_pattern-extraction"
    
    print(f"🧪 Testing save operation for workflow: {workflow_key}")
    
    try:
        # Get the workflow
        workflow = await workflow_service.get_workflow_async(workflow_key)
        if not workflow:
            print(f"❌ Workflow {workflow_key} not found")
            return
        
        print(f"✅ Found workflow: {workflow.metadata.name}")
        print(f"📁 File path: {workflow.file_path}")
        
        # Read the original file
        original_path = Path(workflow.file_path)
        if not original_path.exists():
            print(f"❌ Original file not found: {original_path}")
            return
        
        with open(original_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
            original_json = json.loads(original_content)
        
        print(f"✅ Original file loaded successfully ({len(original_content)} bytes)")
        
        # Create test parameters
        test_parameters = {
            "3.inputs.text": "测试提示词 - Debug Test",
            "10.inputs.steps": 25,
            "10.inputs.cfg": 7.5
        }
        
        print(f"🔧 Test parameters: {test_parameters}")
        
        # Test the save operation
        try:
            result = await workflow_service.save_workflow(
                workflow_key,
                parameters=test_parameters,
                make_backup=True
            )
            
            print(f"✅ Save operation completed")
            print(f"📊 Result: {result}")
            
            # Verify the file was written correctly
            if original_path.exists():
                with open(original_path, 'r', encoding='utf-8') as f:
                    new_content = f.read()
                    
                print(f"📏 New file size: {len(new_content)} bytes")
                
                if len(new_content) == 0:
                    print("❌ CRITICAL: File is empty after save!")
                    
                    # Check if backup exists
                    backup_path = result.get("backup_path")
                    if backup_path and Path(backup_path).exists():
                        print(f"🔄 Restoring from backup: {backup_path}")
                        with open(backup_path, 'r', encoding='utf-8') as bf:
                            backup_content = bf.read()
                        with open(original_path, 'w', encoding='utf-8') as f:
                            f.write(backup_content)
                        print("✅ File restored from backup")
                    
                    return
                
                try:
                    new_json = json.loads(new_content)
                    print("✅ New file is valid JSON")
                    
                    # Verify parameter updates
                    print("\n🔍 Verifying parameter updates:")
                    for param_path, expected_value in test_parameters.items():
                        parts = param_path.split('.')
                        if len(parts) >= 3:
                            node_id = parts[0]
                            if node_id in new_json:
                                node_data = new_json[node_id]
                                if parts[1] == "inputs" and parts[2] in node_data.get("inputs", {}):
                                    actual_value = node_data["inputs"][parts[2]]
                                    status = "✅" if str(actual_value) == str(expected_value) else "❌"
                                    print(f"  {status} {param_path}: {actual_value} (expected: {expected_value})")
                                else:
                                    print(f"  ❌ {param_path}: Path not found in saved file")
                            else:
                                print(f"  ❌ {param_path}: Node {node_id} not found in saved file")
                    
                    # Check file structure integrity
                    print(f"\n📊 File structure check:")
                    print(f"  Original nodes: {len(original_json)}")
                    print(f"  New nodes: {len(new_json)}")
                    
                    if len(new_json) != len(original_json):
                        print("❌ Node count mismatch!")
                    else:
                        print("✅ Node count matches")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ New file is not valid JSON: {e}")
                    print(f"📄 File content preview: {new_content[:200]}...")
            else:
                print("❌ File disappeared after save operation!")
        
        except Exception as e:
            print(f"❌ Save operation failed: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_save_workflow())
