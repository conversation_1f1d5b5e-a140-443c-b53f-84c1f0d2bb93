#!/usr/bin/env python3
"""
Complete Workflow Configuration Persistence Test
Tests the entire round-trip: edit → save → refresh → verify
"""
import json
import sys
import asyncio
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service
from app.ui.workflows.workflow_utils import analyze_workflow_nodes

async def test_complete_workflow_roundtrip():
    """Test the complete workflow configuration persistence"""
    
    print("🧪 Starting Complete Workflow Configuration Persistence Test")
    print("=" * 60)
    
    # Test both workflows
    test_workflows = ["demo_text_generation", "RDY_pattern-extraction"]
    
    for workflow_key in test_workflows:
        print(f"\n📋 Testing workflow: {workflow_key}")
        print("-" * 40)
        
        try:
            # Step 1: Load the workflow
            workflow = await workflow_service.get_workflow_async(workflow_key)
            if not workflow:
                print(f"❌ Workflow {workflow_key} not found")
                continue
            
            print(f"✅ Step 1: Workflow loaded successfully")
            print(f"   Name: {workflow.metadata.name}")
            print(f"   File: {workflow.file_path}")
            
            # Step 2: Analyze editable parameters
            workflow_json = workflow.workflow_json
            if not workflow_json:
                print(f"❌ No workflow JSON found")
                continue
            
            analysis = analyze_workflow_nodes(workflow_json)
            editable_nodes = analysis.get("editable_nodes", [])
            
            if not editable_nodes:
                print(f"ℹ️  No editable parameters found, skipping parameter tests")
                continue
            
            print(f"✅ Step 2: Found {len(editable_nodes)} editable nodes")
            
            # Step 3: Create test parameters
            test_parameters = {}
            original_values = {}
            
            for node in editable_nodes[:2]:  # Test first 2 nodes only
                for param in node["editable_params"][:2]:  # Test first 2 params per node
                    param_path = param["path"]
                    current_value = param["current_value"]
                    original_values[param_path] = current_value
                    
                    # Create test value based on type
                    if isinstance(current_value, str):
                        test_parameters[param_path] = f"TEST_{workflow_key}_{param_path.replace('.', '_')}"
                    elif isinstance(current_value, int):
                        test_parameters[param_path] = current_value + 10
                    elif isinstance(current_value, float):
                        test_parameters[param_path] = round(current_value + 1.5, 2)
                    else:
                        test_parameters[param_path] = f"TEST_VALUE"
            
            if not test_parameters:
                print(f"ℹ️  No testable parameters found")
                continue
            
            print(f"✅ Step 3: Created {len(test_parameters)} test parameters")
            for path, value in test_parameters.items():
                print(f"   {path}: {original_values[path]} → {value}")
            
            # Step 4: Save the workflow with test parameters
            try:
                save_result = await workflow_service.save_workflow(
                    workflow_key,
                    parameters=test_parameters,
                    make_backup=True
                )
                
                print(f"✅ Step 4: Save operation completed")
                print(f"   File: {save_result['file_path']}")
                print(f"   Backup: {save_result.get('backup_path', 'None')}")
                
                # Check verifications
                verifications = save_result.get('verifications', {})
                if verifications:
                    failed_verifications = [k for k, v in verifications.items() if not v.get('ok', True)]
                    if failed_verifications:
                        print(f"⚠️  Some parameters failed verification: {failed_verifications}")
                    else:
                        print(f"✅ All {len(verifications)} parameters verified successfully")
                
            except Exception as save_error:
                print(f"❌ Step 4: Save failed - {save_error}")
                continue
            
            # Step 5: Reload the workflow and verify changes
            try:
                # Clear any cached data and reload
                reloaded_workflow = await workflow_service.get_workflow_async(workflow_key)
                if not reloaded_workflow:
                    print(f"❌ Step 5: Failed to reload workflow")
                    continue
                
                reloaded_json = reloaded_workflow.workflow_json
                print(f"✅ Step 5: Workflow reloaded successfully")
                
                # Verify each parameter was saved correctly
                verification_results = {}
                for param_path, expected_value in test_parameters.items():
                    parts = param_path.split('.')
                    if len(parts) >= 3:
                        node_id = parts[0]
                        if node_id in reloaded_json:
                            node_data = reloaded_json[node_id]
                            if parts[1] == "inputs" and parts[2] in node_data.get("inputs", {}):
                                actual_value = node_data["inputs"][parts[2]]
                                is_correct = str(actual_value) == str(expected_value)
                                verification_results[param_path] = {
                                    "expected": expected_value,
                                    "actual": actual_value,
                                    "correct": is_correct
                                }
                
                # Report verification results
                correct_count = sum(1 for v in verification_results.values() if v["correct"])
                total_count = len(verification_results)
                
                print(f"✅ Step 6: Parameter verification completed")
                print(f"   Correct: {correct_count}/{total_count}")
                
                for param_path, result in verification_results.items():
                    status = "✅" if result["correct"] else "❌"
                    print(f"   {status} {param_path}: {result['actual']} (expected: {result['expected']})")
                
                if correct_count == total_count:
                    print(f"🎉 SUCCESS: All parameters persisted correctly for {workflow_key}!")
                else:
                    print(f"⚠️  PARTIAL: {correct_count}/{total_count} parameters persisted correctly")
                
            except Exception as verify_error:
                print(f"❌ Step 5-6: Verification failed - {verify_error}")
                continue
            
            # Step 7: Restore original values (cleanup)
            try:
                restore_result = await workflow_service.save_workflow(
                    workflow_key,
                    parameters=original_values,
                    make_backup=False
                )
                print(f"✅ Step 7: Original values restored")
                
            except Exception as restore_error:
                print(f"⚠️  Step 7: Failed to restore original values - {restore_error}")
        
        except Exception as e:
            print(f"❌ Test failed for {workflow_key}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🏁 Complete Workflow Configuration Persistence Test Finished")

if __name__ == "__main__":
    asyncio.run(test_complete_workflow_roundtrip())
