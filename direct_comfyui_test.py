#!/usr/bin/env python3
"""
直接测试ComfyUI API，绕过进度条问题
"""

import requests
import json
import time
import subprocess
import os
from pathlib import Path

COMFYUI_BASE_URL = "http://localhost:8188"

def kill_existing_comfyui():
    """杀死现有的ComfyUI进程"""
    print("🔄 检查并关闭现有ComfyUI进程...")
    
    try:
        # 在Windows上查找并杀死ComfyUI进程
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
            capture_output=True, text=True, check=True
        )
        
        lines = result.stdout.strip().split('\n')
        for line in lines[1:]:  # 跳过标题行
            if 'main.py' in line or 'ComfyUI' in line:
                parts = line.split(',')
                if len(parts) >= 2:
                    pid = parts[1].strip('"')
                    print(f"  发现ComfyUI进程: PID {pid}")
                    subprocess.run(['taskkill', '/F', '/PID', pid], check=True)
                    print(f"  ✅ 已终止进程 {pid}")
        
        time.sleep(2)  # 等待进程完全退出
        
    except Exception as e:
        print(f"  ⚠️ 清理进程时出错: {e}")

def start_comfyui_with_fix():
    """启动ComfyUI并应用修复"""
    print("🚀 启动ComfyUI (应用进度条修复)...")
    
    comfyui_dir = r"C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"
    
    if not os.path.exists(comfyui_dir):
        print(f"❌ ComfyUI目录不存在: {comfyui_dir}")
        return None
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'TQDM_DISABLE': '1',
        'PYTHONIOENCODING': 'utf-8',
        'COMFYUI_DISABLE_LOGGING': '1'
    })
    
    # 启动命令
    cmd = [
        'python', 'main.py',
        '--user-directory', r'C:\Users\<USER>\Documents\ComfyUI\user',
        '--input-directory', r'C:\Users\<USER>\Documents\ComfyUI\input', 
        '--output-directory', r'C:\Users\<USER>\Documents\ComfyUI\output',
        '--listen', '127.0.0.1',
        '--port', '8188',
        '--disable-auto-launch',
        '--disable-console-progress-bar'
    ]
    
    try:
        print(f"  命令: {' '.join(cmd)}")
        print(f"  工作目录: {comfyui_dir}")
        print(f"  环境变量: TQDM_DISABLE=1, PYTHONIOENCODING=utf-8")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=comfyui_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"  ✅ ComfyUI进程已启动: PID {process.pid}")
        return process
        
    except Exception as e:
        print(f"  ❌ 启动ComfyUI失败: {e}")
        return None

def wait_for_comfyui_ready(max_wait=60):
    """等待ComfyUI就绪"""
    print("⏳ 等待ComfyUI服务就绪...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{COMFYUI_BASE_URL}/system_stats", timeout=2)
            if response.status_code == 200:
                print("✅ ComfyUI服务已就绪")
                return True
        except:
            pass
        
        time.sleep(2)
        print(".", end="", flush=True)
    
    print(f"\n❌ ComfyUI启动超时 ({max_wait}秒)")
    return False

def test_simple_workflow():
    """测试简单工作流"""
    print("🧪 测试简单工作流...")
    
    # 最简单的文生图工作流
    simple_workflow = {
        "3": {
            "inputs": {
                "seed": 123456,
                "steps": 5,  # 极少步数
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0], 
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "4": {
            "inputs": {
                "ckpt_name": "SDXL\\sdXL_v10VAEFix.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "5": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "text": "simple test",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "7": {
            "inputs": {
                "text": "bad quality",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode"
        },
        "9": {
            "inputs": {
                "filename_prefix": "test_fix",
                "images": ["8", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    # 提交工作流
    try:
        payload = {
            "client_id": "test_client",
            "prompt": simple_workflow
        }
        
        response = requests.post(f"{COMFYUI_BASE_URL}/prompt", json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"✅ 工作流提交成功: {prompt_id}")
            
            if node_errors:
                print(f"❌ 节点错误: {node_errors}")
                return False
            
            # 监控执行
            return monitor_execution(prompt_id)
        else:
            print(f"❌ 提交失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def monitor_execution(prompt_id, max_wait=60):
    """监控执行状态"""
    print(f"⏳ 监控执行状态...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{COMFYUI_BASE_URL}/history/{prompt_id}", timeout=5)
            if response.status_code == 200:
                history = response.json()
                
                if prompt_id in history:
                    status = history[prompt_id].get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)
                    
                    if completed:
                        if status_str == "success":
                            print("🎉 执行成功！")
                            return True
                        else:
                            print(f"❌ 执行失败: {status_str}")
                            return False
                    
                    if status_str == "error":
                        print("❌ 执行出错")
                        messages = status.get("messages", [])
                        for msg in messages[-2:]:
                            if isinstance(msg, list) and len(msg) >= 2:
                                if msg[0] == "execution_error":
                                    error_data = msg[1]
                                    print(f"  错误: {error_data.get('exception_message', '未知')}")
                        return False
            
            time.sleep(2)
            print(".", end="", flush=True)
            
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")
            break
    
    print(f"\n⏰ 监控超时")
    return False

def main():
    """主流程"""
    print("=" * 50)
    print("🔧 ComfyUI进度条问题直接修复测试")
    print("=" * 50)
    
    # 1. 清理现有进程
    kill_existing_comfyui()
    
    # 2. 启动修复版ComfyUI
    process = start_comfyui_with_fix()
    if not process:
        return
    
    # 3. 等待服务就绪
    if not wait_for_comfyui_ready():
        print("❌ ComfyUI启动失败")
        process.terminate()
        return
    
    # 4. 测试工作流
    success = test_simple_workflow()
    
    # 5. 清理
    print("\n🧹 清理进程...")
    process.terminate()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 修复成功！ComfyUI可以正常执行工作流")
        print("✅ 端口占用问题已澄清：不存在冲突")
        print("✅ 进度条问题已解决")
    else:
        print("❌ 仍有问题需要进一步调试")
    print("=" * 50)

if __name__ == "__main__":
    main()
