#!/usr/bin/env python3
"""
测试API格式工作流处理功能
验证所有相关函数是否正确处理API格式的工作流文件
"""

import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from app.ui.workflow_config import classify_workflow_type, analyze_workflow_nodes
from app.ui.workflows.workflow_utils import extract_notes_from_workflow, analyze_workflow_nodes as utils_analyze
from app.services.workflow_service import WorkflowService

def test_api_format_functions():
    """测试API格式处理函数"""
    print("🧪 开始测试API格式工作流处理功能...")
    
    # 加载测试工作流
    test_workflow_path = Path("app/config/workflows/demo_text_generation/workflow.json")
    if not test_workflow_path.exists():
        print(f"❌ 测试工作流文件不存在: {test_workflow_path}")
        return False
    
    try:
        with open(test_workflow_path, 'r', encoding='utf-8') as f:
            workflow_json = json.load(f)
        
        print(f"✅ 成功加载测试工作流，包含 {len(workflow_json)} 个节点")
        
        # 测试1: 工作流类型分类
        print("\n📋 测试1: 工作流类型分类")
        workflow_type = classify_workflow_type(workflow_json)
        print(f"  工作流类型: {workflow_type}")
        
        # 测试2: 节点分析 (workflow_config版本)
        print("\n📋 测试2: 节点分析 (workflow_config)")
        nodes_analysis = analyze_workflow_nodes(workflow_json)
        editable_nodes = nodes_analysis.get("editable_nodes", [])
        print(f"  可编辑节点数量: {len(editable_nodes)}")
        for node in editable_nodes[:3]:  # 显示前3个
            print(f"    - {node.get('title', 'Unknown')} ({node.get('class_type', 'Unknown')})")
        
        # 测试3: 节点分析 (workflow_utils版本)
        print("\n📋 测试3: 节点分析 (workflow_utils)")
        utils_analysis = utils_analyze(workflow_json)
        utils_editable = utils_analysis.get("editable_nodes", [])
        utils_models = utils_analysis.get("model_nodes", [])
        print(f"  可编辑节点数量: {len(utils_editable)}")
        print(f"  模型节点数量: {len(utils_models)}")
        
        # 测试4: 提取说明信息
        print("\n📋 测试4: 提取说明信息")
        notes = extract_notes_from_workflow(workflow_json)
        print(f"  说明信息: {notes[:100]}..." if len(notes) > 100 else f"  说明信息: {notes}")
        
        # 测试5: WorkflowService
        print("\n📋 测试5: WorkflowService")
        workflow_service = WorkflowService()
        
        # 测试API格式验证
        try:
            workflow_service._validate_api_format(workflow_json)
            print("  ✅ API格式验证通过")
        except Exception as e:
            print(f"  ❌ API格式验证失败: {e}")
            return False
        
        # 测试参数提取
        editable_params = workflow_service._extract_editable_params(workflow_json)
        print(f"  提取到 {len(editable_params)} 个可编辑参数:")
        for param_name, param_path in list(editable_params.items())[:3]:
            print(f"    - {param_name}: {param_path}")
        
        print("\n🎉 所有测试通过！API格式处理功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_loading():
    """测试工作流加载功能"""
    print("\n🔄 测试工作流加载功能...")
    
    try:
        workflow_service = WorkflowService()
        
        # 测试本地工作流加载
        local_workflow = workflow_service.get_workflow_local("demo_text_generation")
        if local_workflow:
            print(f"✅ 成功加载本地工作流: {local_workflow.metadata.name}")
            print(f"  节点数量: {len(local_workflow.workflow_json or {})}")
            print(f"  可编辑参数: {len(local_workflow.metadata.editable_params)}")
        else:
            print("❌ 本地工作流加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 API格式工作流处理功能测试")
    print("=" * 60)
    
    # 运行所有测试
    test1_passed = test_api_format_functions()
    test2_passed = test_workflow_loading()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！API格式优化成功")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，需要进一步调试")
        sys.exit(1)
