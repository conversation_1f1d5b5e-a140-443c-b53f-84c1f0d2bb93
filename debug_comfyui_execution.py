#!/usr/bin/env python3
"""
ComfyUI工作流执行问题调试脚本
深入分析工作流提交后不执行的问题
"""

import json
import requests
import time
from pathlib import Path
import sys

# ComfyUI服务器配置
COMFYUI_BASE_URL = "http://localhost:8188"
CLIENT_ID = "freemanhub"

def check_comfyui_server_status():
    """检查ComfyUI服务器状态"""
    print("🔍 检查ComfyUI服务器状态...")
    
    try:
        # 检查基本连接
        response = requests.get(f"{COMFYUI_BASE_URL}/system_stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ ComfyUI服务器运行正常")
            print(f"  系统统计: {stats}")
        else:
            print(f"❌ ComfyUI服务器响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接到ComfyUI服务器: {e}")
        return False
    
    return True

def check_queue_status():
    """检查ComfyUI执行队列状态"""
    print("\n📋 检查ComfyUI执行队列...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/queue", timeout=5)
        if response.status_code == 200:
            queue_data = response.json()
            
            queue_running = queue_data.get("queue_running", [])
            queue_pending = queue_data.get("queue_pending", [])
            
            print(f"  正在执行的任务: {len(queue_running)}")
            print(f"  等待执行的任务: {len(queue_pending)}")
            
            # 显示队列详情
            if queue_running:
                print("  🔄 正在执行:")
                for i, task in enumerate(queue_running[:3]):
                    prompt_id = task[1] if len(task) > 1 else "Unknown"
                    print(f"    {i+1}. Prompt ID: {prompt_id}")
            
            if queue_pending:
                print("  ⏳ 等待执行:")
                for i, task in enumerate(queue_pending[:5]):
                    prompt_id = task[1] if len(task) > 1 else "Unknown"
                    print(f"    {i+1}. Prompt ID: {prompt_id}")
            
            return queue_data
        else:
            print(f"❌ 获取队列状态失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 检查队列状态失败: {e}")
        return None

def check_prompt_status(prompt_id: str):
    """检查特定prompt的执行状态"""
    print(f"\n🎯 检查Prompt {prompt_id} 的执行状态...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/history/{prompt_id}", timeout=5)
        if response.status_code == 200:
            history = response.json()
            if history:
                print(f"✅ 找到执行历史:")
                for key, data in history.items():
                    status = data.get("status", {})
                    print(f"  状态: {status}")
                    
                    if "outputs" in data:
                        outputs = data["outputs"]
                        print(f"  输出节点数: {len(outputs)}")
                        for node_id, output in outputs.items():
                            print(f"    节点 {node_id}: {output}")
                return True
            else:
                print(f"❌ 未找到执行历史，可能还在队列中或执行失败")
                return False
        else:
            print(f"❌ 获取执行历史失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查执行状态失败: {e}")
        return False

def analyze_workflow_file(workflow_path: str):
    """分析工作流文件内容"""
    print(f"\n📄 分析工作流文件: {workflow_path}")
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow_json = json.load(f)
        
        print(f"✅ 工作流文件加载成功")
        print(f"  节点数量: {len(workflow_json)}")
        
        # 检查每个节点
        for node_id, node_data in workflow_json.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                inputs = node_data.get("inputs", {})
                title = node_data.get("_meta", {}).get("title", class_type)
                
                print(f"  节点 {node_id}: {title} ({class_type})")
                
                # 检查输入连接
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) == 2:
                        ref_node, ref_output = input_value
                        if str(ref_node) not in workflow_json:
                            print(f"    ❌ 连接错误: {input_name} 引用不存在的节点 {ref_node}")
                        else:
                            print(f"    🔗 {input_name} -> 节点{ref_node}[{ref_output}]")
                    else:
                        print(f"    📝 {input_name}: {input_value}")
        
        return workflow_json
        
    except Exception as e:
        print(f"❌ 分析工作流文件失败: {e}")
        return None

def test_workflow_submission(workflow_json: dict):
    """测试工作流提交"""
    print(f"\n🚀 测试工作流提交...")
    
    try:
        payload = {
            "client_id": CLIENT_ID,
            "prompt": workflow_json
        }
        
        response = requests.post(
            f"{COMFYUI_BASE_URL}/prompt",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"✅ 工作流提交成功")
            print(f"  Prompt ID: {prompt_id}")
            print(f"  节点错误: {node_errors}")
            
            if node_errors:
                print("❌ 发现节点错误:")
                for node_id, errors in node_errors.items():
                    print(f"    节点 {node_id}: {errors}")
            
            return prompt_id
        else:
            print(f"❌ 工作流提交失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 提交工作流时出错: {e}")
        return None

def main():
    """主调试流程"""
    print("=" * 60)
    print("🔧 ComfyUI工作流执行问题调试")
    print("=" * 60)
    
    # 1. 检查服务器状态
    if not check_comfyui_server_status():
        print("❌ ComfyUI服务器不可用，请先启动服务器")
        return
    
    # 2. 检查队列状态
    queue_data = check_queue_status()
    
    # 3. 检查特定prompt状态
    target_prompt_id = "745aebb4-974d-4dee-8dc2-8a25e0e6f6f9"
    check_prompt_status(target_prompt_id)
    
    # 4. 分析工作流文件
    workflow_path = r"C:\Users\<USER>\Documents\ComfyUI\user\default\workflows\RDY_LOGO-Gen-API.json"
    workflow_json = analyze_workflow_file(workflow_path)
    
    if workflow_json:
        # 5. 重新测试提交
        print(f"\n🔄 重新测试工作流提交...")
        new_prompt_id = test_workflow_submission(workflow_json)
        
        if new_prompt_id:
            print(f"\n⏳ 等待执行结果...")
            time.sleep(3)
            check_prompt_status(new_prompt_id)
    
    print("\n" + "=" * 60)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
