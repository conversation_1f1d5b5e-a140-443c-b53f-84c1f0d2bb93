{"3": {"inputs": {"seed": 3027391384, "steps": 40, "cfg": 8, "sampler_name": "dpmpp_2m_sde", "scheduler": "karras", "denoise": 1, "model": ["16", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "4": {"inputs": {"ckpt_name": "SDXL\\sdXL_v10VAEFix.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "6": {"inputs": {"text": "logomkrdsxl, a logo for a clothing store , vector, text \"Princess Fashion\", <lora:logomkrdsxl:1>, best quality, masterpiece, super smash bros.,", "clip": ["16", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "7": {"inputs": {"text": "blurry, bad art, low quality, deformed, disfigured, ugly, photo, cg, 3d, watermark , signature, repeating letters, malformed letters, doubles, inaccuracy, duplicate, warped text, ,missing letters", "clip": ["16", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "16": {"inputs": {"lora_name": "SDXL\\logomkrdsxl.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}}