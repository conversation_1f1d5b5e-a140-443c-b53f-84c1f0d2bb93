#!/usr/bin/env python3
"""
ComfyUI工作流执行问题解决方案
针对OSError: [Errno 22] Invalid argument错误的修复
"""

import json
import requests
import time
import os
from pathlib import Path

COMFYUI_BASE_URL = "http://localhost:8188"
CLIENT_ID = "freemanhub"

def solution_1_restart_comfyui():
    """解决方案1: 重启ComfyUI服务器"""
    print("🔄 解决方案1: 重启ComfyUI服务器")
    print("  这个错误通常是由于ComfyUI-Manager插件的日志输出问题导致的")
    print("  重启可以清理文件句柄和重置日志状态")
    print("\n  请手动执行以下步骤:")
    print("  1. 关闭ComfyUI应用程序")
    print("  2. 等待5秒")
    print("  3. 重新启动ComfyUI")
    print("  4. 等待完全加载后再测试工作流")

def solution_2_disable_comfyui_manager_logging():
    """解决方案2: 临时禁用ComfyUI-Manager的日志重定向"""
    print("\n🛠️ 解决方案2: 修改ComfyUI-Manager配置")
    
    manager_script_path = Path("C:/Users/<USER>/AppData/Local/Programs/@comfyorgcomfyui-electron/resources/ComfyUI/custom_nodes/ComfyUI-Manager/prestartup_script.py")
    
    if manager_script_path.exists():
        print(f"  找到ComfyUI-Manager脚本: {manager_script_path}")
        print("  建议临时注释掉日志重定向代码")
        print("  或者在ComfyUI启动参数中添加 --disable-manager-logging")
    else:
        print("  未找到ComfyUI-Manager脚本，可能路径不同")

def solution_3_test_simple_workflow():
    """解决方案3: 测试简化的工作流"""
    print("\n🧪 解决方案3: 创建简化测试工作流")
    
    # 创建一个最简单的工作流来测试
    simple_workflow = {
        "3": {
            "inputs": {
                "seed": 123456,
                "steps": 20,
                "cfg": 8.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "4": {
            "inputs": {
                "ckpt_name": "SDXL/sdXL_v10VAEFix.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "5": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent Image"}
        },
        "6": {
            "inputs": {
                "text": "a simple test image",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Positive)"}
        },
        "7": {
            "inputs": {
                "text": "",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "9": {
            "inputs": {
                "filename_prefix": "test_simple",
                "images": ["8", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        }
    }
    
    # 保存简化工作流
    simple_path = "test_simple_workflow.json"
    with open(simple_path, 'w', encoding='utf-8') as f:
        json.dump(simple_workflow, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ 创建简化工作流: {simple_path}")
    
    # 测试提交
    try:
        payload = {
            "client_id": CLIENT_ID,
            "prompt": simple_workflow
        }
        
        response = requests.post(f"{COMFYUI_BASE_URL}/prompt", json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"  ✅ 简化工作流提交成功: {prompt_id}")
            if node_errors:
                print(f"  ❌ 节点错误: {node_errors}")
            else:
                print("  ✅ 无节点错误")
                
                # 等待并检查执行结果
                time.sleep(5)
                check_execution_result(prompt_id)
            
            return prompt_id
        else:
            print(f"  ❌ 简化工作流提交失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 测试简化工作流失败: {e}")
        return None

def check_execution_result(prompt_id: str):
    """检查执行结果"""
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/history/{prompt_id}", timeout=5)
        if response.status_code == 200:
            history = response.json()
            if history:
                for key, data in history.items():
                    status = data.get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)
                    
                    print(f"  执行状态: {status_str}")
                    print(f"  是否完成: {completed}")
                    
                    if status_str == "success" and completed:
                        print("  🎉 工作流执行成功！")
                        return True
                    elif status_str == "error":
                        messages = status.get("messages", [])
                        for msg in messages:
                            if msg[0] == "execution_error":
                                error_info = msg[1]
                                print(f"  ❌ 执行错误: {error_info.get('exception_message', 'Unknown')}")
                        return False
            else:
                print("  ⏳ 还在执行中...")
                return None
    except Exception as e:
        print(f"  ❌ 检查执行结果失败: {e}")
        return None

def solution_4_check_file_permissions():
    """解决方案4: 检查文件权限"""
    print("\n🔐 解决方案4: 检查文件权限")
    
    comfyui_paths = [
        "C:/Users/<USER>/Documents/ComfyUI/output",
        "C:/Users/<USER>/Documents/ComfyUI/temp",
        "C:/Users/<USER>/AppData/Local/Programs/@comfyorgcomfyui-electron/resources/ComfyUI"
    ]
    
    for path in comfyui_paths:
        path_obj = Path(path)
        if path_obj.exists():
            try:
                # 测试写入权限
                test_file = path_obj / "test_write_permission.tmp"
                test_file.write_text("test")
                test_file.unlink()
                print(f"  ✅ {path} - 写入权限正常")
            except Exception as e:
                print(f"  ❌ {path} - 写入权限问题: {e}")
        else:
            print(f"  ❓ {path} - 路径不存在")

def main():
    """主解决流程"""
    print("=" * 60)
    print("🔧 ComfyUI工作流执行问题解决方案")
    print("=" * 60)
    
    print("📋 问题分析:")
    print("  - 工作流JSON格式正确 ✅")
    print("  - ComfyUI服务器运行正常 ✅") 
    print("  - 获得了prompt_id ✅")
    print("  - 但在KSampler节点执行时出现OSError ❌")
    print("  - 错误源于ComfyUI-Manager的日志处理 🎯")
    
    print("\n🛠️ 推荐解决方案:")
    
    # 方案1: 重启
    solution_1_restart_comfyui()
    
    # 方案2: 配置修改
    solution_2_disable_comfyui_manager_logging()
    
    # 方案3: 简化测试
    solution_3_test_simple_workflow()
    
    # 方案4: 权限检查
    solution_4_check_file_permissions()
    
    print("\n📝 建议执行顺序:")
    print("  1. 先尝试重启ComfyUI (最简单)")
    print("  2. 测试简化工作流验证修复")
    print("  3. 如果仍有问题，检查文件权限")
    print("  4. 最后考虑禁用ComfyUI-Manager日志")

if __name__ == "__main__":
    main()
