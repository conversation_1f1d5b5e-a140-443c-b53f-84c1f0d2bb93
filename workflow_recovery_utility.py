#!/usr/bin/env python3
"""
Workflow Recovery Utility
Scans for corrupted workflow files and attempts to recover them from backups
"""
import json
import sys
from pathlib import Path
from datetime import datetime

def scan_and_recover_workflows():
    """Scan for corrupted workflow files and recover from backups"""
    
    workflows_dir = Path("app/config/workflows")
    if not workflows_dir.exists():
        print(f"❌ Workflows directory not found: {workflows_dir}")
        return
    
    print(f"🔍 Scanning workflows in: {workflows_dir}")
    
    corrupted_count = 0
    recovered_count = 0
    
    for workflow_dir in workflows_dir.iterdir():
        if not workflow_dir.is_dir():
            continue
        
        workflow_file = workflow_dir / "workflow.json"
        if not workflow_file.exists():
            print(f"⚠️  {workflow_dir.name}: workflow.json not found")
            continue
        
        # Check if workflow file is corrupted
        try:
            with open(workflow_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    raise ValueError("File is empty")
                json.loads(content)
            print(f"✅ {workflow_dir.name}: OK")
            
        except Exception as e:
            print(f"❌ {workflow_dir.name}: CORRUPTED - {e}")
            corrupted_count += 1
            
            # Look for backup files
            backup_files = list(workflow_dir.glob("workflow.json.bak.*"))
            if backup_files:
                # Sort by modification time, newest first
                backup_files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
                
                for backup_file in backup_files:
                    try:
                        print(f"  🔄 Trying backup: {backup_file.name}")
                        with open(backup_file, 'r', encoding='utf-8') as bf:
                            backup_content = bf.read().strip()
                            if not backup_content:
                                print(f"    ❌ Backup is empty")
                                continue
                            
                            backup_json = json.loads(backup_content)
                            if not isinstance(backup_json, dict) or not backup_json:
                                print(f"    ❌ Backup has invalid format")
                                continue
                            
                            # Backup is valid, restore it
                            print(f"    ✅ Backup is valid, restoring...")
                            
                            # Create a new backup of the corrupted file
                            ts = datetime.now().strftime("%Y%m%d_%H%M%S")
                            corrupted_backup = workflow_file.with_suffix(f".corrupted.{ts}")
                            try:
                                if workflow_file.stat().st_size > 0:
                                    workflow_file.rename(corrupted_backup)
                                    print(f"    📁 Corrupted file backed up as: {corrupted_backup.name}")
                            except:
                                pass
                            
                            # Restore from backup
                            with open(workflow_file, 'w', encoding='utf-8') as wf:
                                json.dump(backup_json, wf, ensure_ascii=False, indent=2)
                            
                            print(f"    ✅ Successfully restored from {backup_file.name}")
                            recovered_count += 1
                            break
                            
                    except Exception as backup_error:
                        print(f"    ❌ Backup {backup_file.name} is also corrupted: {backup_error}")
                        continue
                
                if recovered_count == corrupted_count:
                    continue  # Successfully recovered
                
            print(f"  ❌ No valid backups found for {workflow_dir.name}")
    
    print(f"\n📊 Summary:")
    print(f"  Corrupted workflows: {corrupted_count}")
    print(f"  Successfully recovered: {recovered_count}")
    print(f"  Still corrupted: {corrupted_count - recovered_count}")
    
    if recovered_count > 0:
        print(f"\n✅ Recovery completed! {recovered_count} workflows restored.")
    elif corrupted_count > 0:
        print(f"\n⚠️  {corrupted_count} workflows remain corrupted and need manual attention.")
    else:
        print(f"\n✅ All workflows are healthy!")

def create_workflow_health_report():
    """Create a detailed health report of all workflows"""
    
    workflows_dir = Path("app/config/workflows")
    if not workflows_dir.exists():
        print(f"❌ Workflows directory not found: {workflows_dir}")
        return
    
    report_file = Path("workflow_health_report.txt")
    
    with open(report_file, 'w', encoding='utf-8') as report:
        report.write(f"Workflow Health Report\n")
        report.write(f"Generated: {datetime.now().isoformat()}\n")
        report.write(f"=" * 50 + "\n\n")
        
        for workflow_dir in workflows_dir.iterdir():
            if not workflow_dir.is_dir():
                continue
            
            report.write(f"Workflow: {workflow_dir.name}\n")
            report.write(f"-" * 30 + "\n")
            
            workflow_file = workflow_dir / "workflow.json"
            metadata_file = workflow_dir / "metadata.json"
            
            # Check workflow.json
            if workflow_file.exists():
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if not content:
                            report.write(f"❌ workflow.json: EMPTY\n")
                        else:
                            workflow_json = json.loads(content)
                            node_count = len(workflow_json) if isinstance(workflow_json, dict) else 0
                            file_size = workflow_file.stat().st_size
                            report.write(f"✅ workflow.json: OK ({node_count} nodes, {file_size} bytes)\n")
                except Exception as e:
                    report.write(f"❌ workflow.json: CORRUPTED - {e}\n")
            else:
                report.write(f"❌ workflow.json: MISSING\n")
            
            # Check metadata.json
            if metadata_file.exists():
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        report.write(f"✅ metadata.json: OK\n")
                        report.write(f"   Name: {metadata.get('name', 'N/A')}\n")
                        report.write(f"   Tags: {metadata.get('tags', [])}\n")
                except Exception as e:
                    report.write(f"❌ metadata.json: CORRUPTED - {e}\n")
            else:
                report.write(f"⚠️  metadata.json: MISSING\n")
            
            # Check backups
            backup_files = list(workflow_dir.glob("workflow.json.bak.*"))
            if backup_files:
                report.write(f"📁 Backups: {len(backup_files)} files\n")
                for backup in sorted(backup_files, key=lambda p: p.stat().st_mtime, reverse=True)[:3]:
                    mtime = datetime.fromtimestamp(backup.stat().st_mtime)
                    report.write(f"   - {backup.name} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})\n")
            else:
                report.write(f"📁 Backups: None\n")
            
            report.write(f"\n")
    
    print(f"📄 Health report saved to: {report_file}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "report":
        create_workflow_health_report()
    else:
        scan_and_recover_workflows()
