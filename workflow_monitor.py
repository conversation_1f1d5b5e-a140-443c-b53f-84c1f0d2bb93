#!/usr/bin/env python3
"""
Workflow File Monitor
Monitors workflow files for corruption and provides real-time diagnostics
"""
import json
import time
import sys
from pathlib import Path
from datetime import datetime
import hashlib

def calculate_file_hash(file_path):
    """Calculate MD5 hash of a file"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def check_workflow_integrity(workflow_path):
    """Check if a workflow file is valid"""
    try:
        if not workflow_path.exists():
            return {"status": "missing", "error": "File does not exist"}
        
        if workflow_path.stat().st_size == 0:
            return {"status": "empty", "error": "File is empty"}
        
        with open(workflow_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                return {"status": "empty", "error": "File content is empty"}
            
            workflow_json = json.loads(content)
            if not isinstance(workflow_json, dict):
                return {"status": "invalid", "error": "Not a valid workflow object"}
            
            if not workflow_json:
                return {"status": "empty", "error": "Workflow object is empty"}
            
            node_count = len(workflow_json)
            file_size = workflow_path.stat().st_size
            file_hash = calculate_file_hash(workflow_path)
            
            return {
                "status": "valid",
                "node_count": node_count,
                "file_size": file_size,
                "hash": file_hash,
                "last_modified": datetime.fromtimestamp(workflow_path.stat().st_mtime).isoformat()
            }
    
    except json.JSONDecodeError as e:
        return {"status": "json_error", "error": f"JSON decode error: {e}"}
    except UnicodeDecodeError as e:
        return {"status": "encoding_error", "error": f"Encoding error: {e}"}
    except Exception as e:
        return {"status": "error", "error": str(e)}

def monitor_workflows(duration_seconds=60):
    """Monitor workflow files for changes and corruption"""
    
    workflows_dir = Path("app/config/workflows")
    if not workflows_dir.exists():
        print(f"❌ Workflows directory not found: {workflows_dir}")
        return
    
    print(f"🔍 Monitoring workflows for {duration_seconds} seconds...")
    print(f"📁 Directory: {workflows_dir}")
    
    # Get initial state
    workflow_files = {}
    for workflow_dir in workflows_dir.iterdir():
        if workflow_dir.is_dir():
            workflow_file = workflow_dir / "workflow.json"
            workflow_files[workflow_dir.name] = {
                "path": workflow_file,
                "last_check": None,
                "last_status": None,
                "corruption_count": 0
            }
    
    print(f"📊 Monitoring {len(workflow_files)} workflows")
    
    start_time = time.time()
    check_interval = 2  # Check every 2 seconds
    
    while time.time() - start_time < duration_seconds:
        current_time = datetime.now()
        
        for workflow_name, info in workflow_files.items():
            workflow_path = info["path"]
            current_status = check_workflow_integrity(workflow_path)
            
            # Compare with last status
            if info["last_status"] is None:
                # First check
                status_icon = "✅" if current_status["status"] == "valid" else "❌"
                print(f"{current_time.strftime('%H:%M:%S')} {status_icon} {workflow_name}: {current_status['status']}")
                if current_status["status"] == "valid":
                    print(f"    📊 {current_status['node_count']} nodes, {current_status['file_size']} bytes")
            
            elif current_status["status"] != info["last_status"]["status"]:
                # Status changed
                if current_status["status"] == "valid" and info["last_status"]["status"] != "valid":
                    print(f"{current_time.strftime('%H:%M:%S')} 🔄 {workflow_name}: RECOVERED → {current_status['status']}")
                elif current_status["status"] != "valid" and info["last_status"]["status"] == "valid":
                    print(f"{current_time.strftime('%H:%M:%S')} 🚨 {workflow_name}: CORRUPTED → {current_status['status']}")
                    print(f"    ❌ Error: {current_status.get('error', 'Unknown')}")
                    info["corruption_count"] += 1
                else:
                    print(f"{current_time.strftime('%H:%M:%S')} 🔄 {workflow_name}: {info['last_status']['status']} → {current_status['status']}")
            
            elif current_status["status"] == "valid" and info["last_status"]["status"] == "valid":
                # Check if file content changed
                if current_status.get("hash") != info["last_status"].get("hash"):
                    print(f"{current_time.strftime('%H:%M:%S')} 📝 {workflow_name}: File updated")
                    print(f"    📊 {current_status['node_count']} nodes, {current_status['file_size']} bytes")
            
            info["last_status"] = current_status
            info["last_check"] = current_time
        
        time.sleep(check_interval)
    
    # Summary
    print(f"\n📊 Monitoring Summary:")
    for workflow_name, info in workflow_files.items():
        final_status = info["last_status"]
        corruption_count = info["corruption_count"]
        
        if final_status["status"] == "valid":
            print(f"✅ {workflow_name}: Healthy (corruptions: {corruption_count})")
        else:
            print(f"❌ {workflow_name}: {final_status['status']} (corruptions: {corruption_count})")
            print(f"   Error: {final_status.get('error', 'Unknown')}")

def fix_workflow_encoding():
    """Fix encoding issues in workflow files"""
    
    workflows_dir = Path("app/config/workflows")
    
    for workflow_dir in workflows_dir.iterdir():
        if not workflow_dir.is_dir():
            continue
        
        workflow_file = workflow_dir / "workflow.json"
        if not workflow_file.exists():
            continue
        
        print(f"🔧 Checking encoding for {workflow_dir.name}...")
        
        try:
            # Try to read with different encodings
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(workflow_file, 'r', encoding=encoding) as f:
                        content = f.read()
                        workflow_json = json.loads(content)
                        
                        # Successfully read, now rewrite with proper UTF-8
                        backup_path = workflow_file.with_suffix(f".encoding_fix_backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                        workflow_file.rename(backup_path)
                        
                        with open(workflow_file, 'w', encoding='utf-8', newline='\n') as f:
                            json.dump(workflow_json, f, ensure_ascii=False, indent=2)
                        
                        print(f"✅ Fixed encoding for {workflow_dir.name} (was {encoding})")
                        print(f"   Backup: {backup_path.name}")
                        break
                        
                except (UnicodeDecodeError, json.JSONDecodeError):
                    continue
            else:
                print(f"❌ Could not fix encoding for {workflow_dir.name}")
        
        except Exception as e:
            print(f"❌ Error fixing {workflow_dir.name}: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "monitor":
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            monitor_workflows(duration)
        elif sys.argv[1] == "fix-encoding":
            fix_workflow_encoding()
        else:
            print("Usage: python workflow_monitor.py [monitor [duration]|fix-encoding]")
    else:
        # Default: quick integrity check
        workflows_dir = Path("app/config/workflows")
        for workflow_dir in workflows_dir.iterdir():
            if workflow_dir.is_dir():
                workflow_file = workflow_dir / "workflow.json"
                status = check_workflow_integrity(workflow_file)
                status_icon = "✅" if status["status"] == "valid" else "❌"
                print(f"{status_icon} {workflow_dir.name}: {status['status']}")
                if status["status"] != "valid":
                    print(f"   Error: {status.get('error', 'Unknown')}")
