# ComfyUI启动脚本 - 修复进度条问题
# 使用方法: .\Start-ComfyUI-Fixed.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 启动ComfyUI (修复进度条问题)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

# ComfyUI安装路径
$ComfyUIPath = "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

# 检查路径是否存在
if (-not (Test-Path $ComfyUIPath)) {
    Write-Host "❌ ComfyUI路径不存在: $ComfyUIPath" -ForegroundColor Red
    exit 1
}

Write-Host "📁 切换到ComfyUI目录: $ComfyUIPath" -ForegroundColor Yellow
Set-Location $ComfyUIPath

Write-Host "🔧 设置环境变量..." -ForegroundColor Yellow
$env:TQDM_DISABLE = "1"
$env:PYTHONIOENCODING = "utf-8"
$env:COMFYUI_DISABLE_LOGGING = "1"

Write-Host "✅ 环境变量设置完成:" -ForegroundColor Green
Write-Host "  TQDM_DISABLE=1 (禁用tqdm进度条)" -ForegroundColor Gray
Write-Host "  PYTHONIOENCODING=utf-8 (修复编码问题)" -ForegroundColor Gray
Write-Host "  COMFYUI_DISABLE_LOGGING=1 (禁用问题日志)" -ForegroundColor Gray

Write-Host ""
Write-Host "🔥 启动ComfyUI服务器..." -ForegroundColor Green
Write-Host "💡 关键修复参数: --disable-console-progress-bar" -ForegroundColor Cyan

# 启动ComfyUI
$arguments = @(
    "main.py",
    "--user-directory", "C:\Users\<USER>\Documents\ComfyUI\user",
    "--input-directory", "C:\Users\<USER>\Documents\ComfyUI\input",
    "--output-directory", "C:\Users\<USER>\Documents\ComfyUI\output",
    "--listen", "127.0.0.1",
    "--port", "8188",
    "--disable-auto-launch",
    "--disable-console-progress-bar"
)

Write-Host "📋 启动参数:" -ForegroundColor Yellow
foreach ($arg in $arguments) {
    Write-Host "  $arg" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🎯 启动中... (按Ctrl+C停止)" -ForegroundColor Green

try {
    & python $arguments
}
catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "🏁 ComfyUI已退出" -ForegroundColor Yellow
