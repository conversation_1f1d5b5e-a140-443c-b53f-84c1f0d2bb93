import httpx
import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
import json
from pathlib import Path

from .schemas import ComfyUIPromptRequest, ComfyUIPromptResponse, ComfyUIHistoryItem

class ComfyUIClient:
    def __init__(self):
        self.base_url = os.getenv("COMFYUI_BASE_URL", "http://localhost:8188")
        self.timeout = int(os.getenv("API_TIMEOUT", "30"))
        
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送 HTTP 请求到 ComfyUI"""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.request(method, url, **kwargs)
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                raise Exception(f"ComfyUI API 错误 {e.response.status_code}: {e.response.text}")
            except httpx.RequestError as e:
                raise Exception(f"ComfyUI 连接错误: {str(e)}")
            except Exception as e:
                raise Exception(f"ComfyUI 请求失败: {str(e)}")

    async def submit_prompt(self, workflow: Dict[str, Any], client_id: Optional[str] = None) -> ComfyUIPromptResponse:
        """提交工作流到 ComfyUI"""
        data = {
            "prompt": workflow,
            "client_id": client_id or "freemanhub"
        }

        try:
            print(f"🔗 向ComfyUI发送请求: POST {self.base_url}/prompt")
            print(f"📦 请求数据: client_id={data['client_id']}, workflow节点数={len(workflow)}")

            result = await self._request("POST", "/prompt", json=data)
            print(f"📨 ComfyUI响应: {result}")

            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})

            if not prompt_id:
                raise Exception("ComfyUI 未返回有效的 prompt_id")

            if node_errors:
                error_details = []
                for node_id, errors in node_errors.items():
                    error_details.append(f"节点{node_id}: {errors}")
                raise Exception(f"工作流节点错误: {'; '.join(error_details)}")

            print(f"🎯 成功获取prompt_id: {prompt_id}")
            return ComfyUIPromptResponse(prompt_id=prompt_id)
        except Exception as e:
            print(f"❌ 提交工作流异常: {str(e)}")
            raise Exception(f"提交工作流失败: {str(e)}")

    async def wait_for_completion(self, prompt_id: str, timeout_seconds: int = 300) -> Dict[str, Any]:
        """等待工作流执行完成"""
        start_time = time.time()

        while time.time() - start_time < timeout_seconds:
            try:
                # 检查执行历史
                history = await self._request("GET", f"/history/{prompt_id}")

                if prompt_id in history:
                    status = history[prompt_id].get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)

                    print(f"📊 Prompt {prompt_id} 状态: {status_str}, 完成: {completed}")

                    if completed:
                        if status_str == "success":
                            outputs = history[prompt_id].get("outputs", {})
                            return {
                                "success": True,
                                "status": status_str,
                                "outputs": outputs,
                                "prompt_id": prompt_id
                            }
                        else:
                            # 提取错误信息
                            messages = status.get("messages", [])
                            error_msg = "执行失败"
                            for msg in messages:
                                if isinstance(msg, list) and len(msg) >= 2:
                                    if msg[0] == "execution_error":
                                        error_data = msg[1]
                                        error_msg = f"节点{error_data.get('node_id')}({error_data.get('node_type')}): {error_data.get('exception_message', '未知错误')}"
                                        break

                            return {
                                "success": False,
                                "status": status_str,
                                "error": error_msg,
                                "prompt_id": prompt_id
                            }

                    # 如果状态是error，直接返回失败
                    if status_str == "error":
                        messages = status.get("messages", [])
                        error_msg = "执行出错"
                        for msg in messages:
                            if isinstance(msg, list) and len(msg) >= 2:
                                if msg[0] == "execution_error":
                                    error_data = msg[1]
                                    error_msg = f"节点{error_data.get('node_id')}({error_data.get('node_type')}): {error_data.get('exception_message', '未知错误')}"
                                    break

                        return {
                            "success": False,
                            "status": status_str,
                            "error": error_msg,
                            "prompt_id": prompt_id
                        }

                # 等待一段时间再检查
                await asyncio.sleep(2)

            except Exception as e:
                print(f"❌ 监控执行状态时出错: {e}")
                break

        # 超时
        return {
            "success": False,
            "status": "timeout",
            "error": f"执行超时 ({timeout_seconds}秒)",
            "prompt_id": prompt_id
        }

    async def get_history(self, prompt_id: Optional[str] = None) -> List[ComfyUIHistoryItem]:
        """获取执行历史"""
        try:
            endpoint = f"/history/{prompt_id}" if prompt_id else "/history"
            result = await self._request("GET", endpoint)
            
            history_items = []
            for pid, data in result.items():
                history_items.append(ComfyUIHistoryItem(
                    prompt_id=pid,
                    status=data.get("status", {}),
                    outputs=data.get("outputs")
                ))
            
            return history_items
        except Exception as e:
            raise Exception(f"获取执行历史失败: {str(e)}")

    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            result = await self._request("GET", "/queue")
            return result
        except Exception as e:
            raise Exception(f"获取队列状态失败: {str(e)}")

    async def cancel_prompt(self, prompt_id: str) -> bool:
        """取消执行中的任务"""
        try:
            data = {"delete": [prompt_id]}
            await self._request("POST", "/queue", json=data)
            return True
        except Exception as e:
            raise Exception(f"取消任务失败: {str(e)}")

    async def get_image(self, filename: str, subfolder: str = "", folder_type: str = "output") -> bytes:
        """获取生成的图片"""
        try:
            params = {
                "filename": filename,
                "type": folder_type
            }
            if subfolder:
                params["subfolder"] = subfolder
                
            url = f"{self.base_url.rstrip('/')}/view"
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.content
        except Exception as e:
            raise Exception(f"获取图片失败: {str(e)}")

    async def check_connection(self) -> bool:
        """检查 ComfyUI 连接状态"""
        try:
            # 尝试获取队列状态来验证连接
            await self.get_queue_status()
            return True
        except Exception:
            return False

    def update_workflow_parameters(self, workflow: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """根据参数键路径更新工作流 JSON。
        - 支持 Dict 结构（标准 ComfyUI 导出）和 List 索引（如 widgets_values）。
        - 示例：{"4.inputs.text": "新的提示词"} 或 {"7.widgets_values.3": 42}
        """
        import copy

        updated_workflow = copy.deepcopy(workflow)

        # 清理无效的节点
        updated_workflow = self._clean_workflow_json(updated_workflow)

        for param_path, value in (parameters or {}).items():
            try:
                parts = [p for p in str(param_path).split('.') if p != '']
                if not parts:
                    continue

                # 优先处理 ComfyUI "nodes" 列表结构：
                # 期望路径形如 "<node_id>.widgets_values.<index>"
                if isinstance(updated_workflow, dict) and isinstance(updated_workflow.get("nodes"), list):
                    nodes = updated_workflow.get("nodes", [])
                    node_id = parts[0]
                    # 若路径以节点ID开头但后续字段不识别，也不要创建顶层同名数字键
                    # 在 nodes 列表中查找 id 匹配的节点
                    target = None
                    for node in nodes:
                        nid = node.get("id")
                        if str(nid) == str(node_id):
                            target = node
                            break
                    if target is None:
                        # 未找到节点，跳过该参数
                        print(f"警告：未在 nodes 列表中找到 id={node_id} 的节点，忽略 {param_path}")
                        continue

                    # 支持 widgets_values 更新
                    if parts[1] == "widgets_values":
                        # 情况1：替换整个 widgets_values 列表（路径形如 <id>.widgets_values）
                        if len(parts) == 2:
                            if isinstance(value, list):
                                target["widgets_values"] = value
                            else:
                                target["widgets_values"] = [value]
                            continue
                        # 情况2：设置某个索引值（路径形如 <id>.widgets_values.<index>）
                        if len(parts) >= 3:
                            try:
                                idx = int(parts[2])
                            except ValueError:
                                print(f"警告：widgets_values 索引无效: {parts[2]}")
                                continue
                            wv = target.get("widgets_values")
                            if not isinstance(wv, list):
                                wv = []
                                target["widgets_values"] = wv
                            while len(wv) <= idx:
                                wv.append(None)
                            wv[idx] = value
                            # 该路径处理完毕
                            continue

                    # 可选：处理 inputs 名称（若路径形如 <id>.inputs.<name>）
                    if len(parts) >= 3 and parts[1] == "inputs":
                        # 在 nodes-list 结构中，inputs 通常是关于连接/定义的描述，不直接存储值。
                        # 为了兼容少数变体，我们尝试在 inputs 数组里找到 name 匹配项并写入 default/value。
                        node_inputs = target.get("inputs")
                        if isinstance(node_inputs, list):
                            input_name = parts[2]
                            for inp in node_inputs:
                                if isinstance(inp, dict) and inp.get("name") == input_name:
                                    # 优先写到 widgets_values；否则写入 default/value 作占位
                                    if "default" in inp:
                                        inp["default"] = value
                                    else:
                                        inp["value"] = value
                                    break
                        # 完成处理
                        continue

                # 兜底：字典映射结构（标准 ComfyUI 导出），按路径逐段创建并赋值
                current = updated_workflow
                for i, part in enumerate(parts[:-1]):
                    next_part = parts[i + 1]

                    if isinstance(current, list):
                        # 列表索引访问
                        try:
                            idx = int(part)
                        except ValueError:
                            raise Exception(f"路径段 '{part}' 不是有效的列表索引")
                        while len(current) <= idx:
                            current.append({} if not next_part.isdigit() else [])
                        current = current[idx]
                        continue

                    # dict 访问，必要时创建
                    if part not in current or current[part] is None:
                        current[part] = [] if next_part.isdigit() else {}
                    current = current[part]

                last = parts[-1]
                if isinstance(current, list):
                    idx = int(last)
                    while len(current) <= idx:
                        current.append(None)
                    current[idx] = value
                else:
                    current[last] = value

            except Exception as e:
                print(f"警告：无法设置参数 {param_path}: {str(e)}")

        return updated_workflow

    async def get_workflows_from_directory(self, workflow_dir: str) -> List[Dict[str, Any]]:
        """从指定目录递归读取 ComfyUI 工作流文件"""
        import os
        import json
        from pathlib import Path
        
        workflows = []
        
        try:
            workflow_path = Path(workflow_dir)
            
            if not workflow_path.exists() or not workflow_path.is_dir():
                raise Exception(f"工作流目录不存在: {workflow_dir}")
            
            # 递归查找所有 .json 文件
            for json_file in workflow_path.rglob("*.json"):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        workflow_data = json.load(f)
                    
                    # 计算相对路径来确定目录结构
                    relative_path = json_file.relative_to(workflow_path)
                    
                    # 获取父目录路径（用于分组）
                    parent_dir = str(relative_path.parent) if relative_path.parent != Path('.') else ""
                    
                    # 提取工作流信息
                    workflow_info = {
                        "name": json_file.stem,  # 文件名作为工作流名
                        "display_name": json_file.stem,  # 显示名称
                        "file_path": str(json_file),
                        "relative_path": str(relative_path),
                        "parent_dir": parent_dir,  # 父目录路径，用于分组
                        "workflow_json": workflow_data,
                        "last_modified": json_file.stat().st_mtime,
                        "size": json_file.stat().st_size
                    }
                    
                    workflows.append(workflow_info)
                    
                except Exception as e:
                    # 记录错误但继续处理其他文件
                    print(f"警告：无法读取工作流文件 {json_file}: {str(e)}")
            
            return workflows
            
        except Exception as e:
            raise Exception(f"读取工作流目录失败: {str(e)}")

    def _clean_workflow_json(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """清理API格式工作流JSON，移除无效节点"""
        if not isinstance(workflow, dict):
            return workflow

        # 专注于API格式处理（节点ID作为顶级键）
        print(f"🔍 处理API格式工作流，包含 {len(workflow)} 个顶级键")

        cleaned_workflow = {}
        # API格式中可能存在的元数据键
        metadata_keys = {'id', 'revision', 'last_node_id', 'last_link_id', 'nodes', 'links', 'groups', 'config', 'extra', 'version'}

        for node_id, node_data in workflow.items():
            # 跳过元数据键
            if node_id in metadata_keys:
                print(f"⚠️ 跳过元数据键: {node_id}")
                continue

            # 跳过无效的节点ID
            if node_id in ['#id', '#', '']:
                print(f"⚠️ 跳过无效节点ID: '{node_id}'")
                continue

            # 确保节点数据是字典
            if not isinstance(node_data, dict):
                print(f"⚠️ 跳过无效节点数据: {node_id}")
                continue

            # 确保节点有class_type
            if 'class_type' not in node_data:
                print(f"⚠️ 跳过缺少class_type的节点: {node_id}")
                continue

            # 确保class_type不为空
            if not node_data.get('class_type'):
                print(f"⚠️ 跳过class_type为空的节点: {node_id}")
                continue

            cleaned_workflow[node_id] = node_data

        print(f"🧹 API格式工作流清理完成: {len(workflow)} -> {len(cleaned_workflow)} 节点")
        return cleaned_workflow

# 全局 ComfyUI 客户端实例
comfy_client = ComfyUIClient()
