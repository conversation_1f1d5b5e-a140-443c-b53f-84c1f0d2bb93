#!/usr/bin/env python3
"""
Final Validation Test
Comprehensive test to ensure the model loader contamination bug is completely fixed
"""
import json
import sys
import asyncio
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.workflow_service import workflow_service
from app.api.comfy_client import ComfyUIClient

async def final_validation_test():
    """Final comprehensive validation test"""
    
    print("🎯 Final Validation Test - Model Loader Contamination Fix")
    print("=" * 70)
    
    # Create a realistic test workflow with model loaders
    test_workflow_with_loaders = {
        "1": {
            "inputs": {
                "text": "Original prompt text",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Positive Prompt"}
        },
        "2": {
            "inputs": {
                "clip_name1": "Flux/clip_l.safetensors",
                "clip_name2": "Flux/t5xxl_fp16.safetensors"
            },
            "class_type": "DualCLIPLoader",
            "_meta": {"title": "CLIP Loader"}
        },
        "3": {
            "inputs": {
                "unet_name": "Flux/flux1-dev-kontext_fp8_scaled.safetensors"
            },
            "class_type": "UNETLoader", 
            "_meta": {"title": "UNET Loader"}
        },
        "4": {
            "inputs": {
                "vae_name": "Flux/ae.sft"
            },
            "class_type": "VAELoader",
            "_meta": {"title": "VAE Loader"}
        },
        "5": {
            "inputs": {
                "lora_name": "kontext_毛毯提取图案印花_1.0",
                "strength_model": 1.0
            },
            "class_type": "LoraLoaderModelOnly",
            "_meta": {"title": "LoRA Loader"}
        },
        "6": {
            "inputs": {
                "steps": 20,
                "cfg": 7.0,
                "seed": 42,
                "sampler_name": "euler",
                "scheduler": "simple"
            },
            "class_type": "KSampler",
            "_meta": {"title": "Sampler"}
        }
    }
    
    print(f"📊 Created test workflow with {len(test_workflow_with_loaders)} nodes")
    
    # Step 1: Extract editable parameters
    from app.ui.workflows.workflow_utils import analyze_workflow_nodes
    analysis = analyze_workflow_nodes(test_workflow_with_loaders)
    editable_nodes = analysis.get("editable_nodes", [])
    
    print(f"\n🔧 Step 1: Parameter Extraction")
    print(f"   Found {len(editable_nodes)} editable nodes:")
    
    editable_params = {}
    for node in editable_nodes:
        node_id = node["id"]
        class_type = node["class_type"]
        print(f"     Node {node_id} ({class_type}): {node['title']}")
        
        for param in node["editable_params"]:
            param_path = param["path"]
            current_value = param["current_value"]
            editable_params[param_path] = current_value
            print(f"       - {param_path}: {current_value}")
    
    # Step 2: Create test parameter changes (only for truly editable nodes)
    test_parameters = {
        "1.inputs.text": "Modified prompt text - TESTING",
        "6.inputs.steps": 25,
        "6.inputs.cfg": 8.5
    }
    
    print(f"\n🧪 Step 2: Applying Test Parameter Changes")
    print(f"   Test parameters: {test_parameters}")
    
    # Step 3: Apply parameter updates
    client = ComfyUIClient()
    updated_workflow = client.update_workflow_parameters(test_workflow_with_loaders, test_parameters)
    
    print(f"\n🔍 Step 3: Verifying Parameter Updates")
    
    # Check that ONLY intended parameters were changed
    unintended_changes = []
    intended_changes = []
    
    for node_id, original_node in test_workflow_with_loaders.items():
        updated_node = updated_workflow.get(node_id, {})
        original_inputs = original_node.get("inputs", {})
        updated_inputs = updated_node.get("inputs", {})
        class_type = original_node.get("class_type", "")
        
        for input_name, original_value in original_inputs.items():
            updated_value = updated_inputs.get(input_name)
            param_path = f"{node_id}.inputs.{input_name}"
            
            if updated_value != original_value:
                if param_path in test_parameters:
                    intended_changes.append({
                        "path": param_path,
                        "node_type": class_type,
                        "original": original_value,
                        "updated": updated_value,
                        "expected": test_parameters[param_path]
                    })
                else:
                    unintended_changes.append({
                        "path": param_path,
                        "node_type": class_type,
                        "original": original_value,
                        "updated": updated_value
                    })
    
    # Report results
    print(f"\n📊 Verification Results:")
    print(f"   Intended changes: {len(intended_changes)}")
    print(f"   Unintended changes: {len(unintended_changes)}")
    
    # Show intended changes
    if intended_changes:
        print(f"\n✅ Intended Changes (GOOD):")
        for change in intended_changes:
            expected_match = str(change["updated"]) == str(change["expected"])
            status = "✅" if expected_match else "⚠️"
            print(f"   {status} {change['path']} ({change['node_type']}):")
            print(f"      {change['original']} → {change['updated']}")
            if not expected_match:
                print(f"      Expected: {change['expected']}")
    
    # Show unintended changes (CRITICAL ERRORS)
    if unintended_changes:
        print(f"\n🚨 Unintended Changes (CRITICAL ERRORS):")
        for change in unintended_changes:
            print(f"   ❌ {change['path']} ({change['node_type']}):")
            print(f"      {change['original']} → {change['updated']}")
            print(f"      🚨 This node should NOT have been modified!")
    else:
        print(f"\n✅ No Unintended Changes Detected - Model Loaders Protected!")
    
    # Step 4: Verify specific problematic model loader values are preserved
    print(f"\n🔍 Step 4: Verifying Model Loader Values Preservation")
    
    critical_model_values = {
        "2": {"clip_name1": "Flux/clip_l.safetensors", "clip_name2": "Flux/t5xxl_fp16.safetensors"},
        "3": {"unet_name": "Flux/flux1-dev-kontext_fp8_scaled.safetensors"},
        "4": {"vae_name": "Flux/ae.sft"},
        "5": {"lora_name": "kontext_毛毯提取图案印花_1.0"}
    }
    
    all_preserved = True
    for node_id, expected_values in critical_model_values.items():
        updated_node = updated_workflow.get(node_id, {})
        updated_inputs = updated_node.get("inputs", {})
        
        print(f"   Node {node_id} ({updated_node.get('class_type', 'Unknown')}):")
        
        for param_name, expected_value in expected_values.items():
            actual_value = updated_inputs.get(param_name)
            preserved = actual_value == expected_value
            status = "✅" if preserved else "🚨"
            
            print(f"     {status} {param_name}: {actual_value}")
            if not preserved:
                print(f"        Expected: {expected_value}")
                all_preserved = False
    
    # Final verdict
    print(f"\n🏁 FINAL VALIDATION RESULTS:")
    print(f"=" * 40)
    
    success_criteria = [
        (len(unintended_changes) == 0, "No unintended parameter changes"),
        (len(intended_changes) == len(test_parameters), "All intended changes applied"),
        (all_preserved, "All model loader values preserved")
    ]
    
    all_passed = True
    for passed, description in success_criteria:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status}: {description}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 SUCCESS: Model loader contamination bug is COMPLETELY FIXED!")
        print(f"   ✅ Model loader nodes are properly protected")
        print(f"   ✅ Only intended parameters are modified")
        print(f"   ✅ File paths are preserved correctly")
    else:
        print(f"\n🚨 FAILURE: Bug still exists, requires further investigation")
    
    return all_passed

if __name__ == "__main__":
    result = asyncio.run(final_validation_test())
    sys.exit(0 if result else 1)
