import os
import json
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..api.schemas import WorkflowInfo, WorkflowMetadata, WorkflowTag
from ..api.comfy_client import comfy_client

class WorkflowService:
    def __init__(self):
        # 移除本地工作流目录，使用ComfyUI目录
        self.comfyui_workflow_dir = None  # 将由用户指定
        
    def set_comfyui_workflow_dir(self, workflow_dir: str):
        """设置ComfyUI工作流目录"""
        self.comfyui_workflow_dir = workflow_dir
    
    async def list_workflows(self) -> List[WorkflowInfo]:
        """获取所有工作流列表"""
        if not self.comfyui_workflow_dir:
            return []
            
        try:
            # 从ComfyUI目录读取工作流
            comfy_workflows = await comfy_client.get_workflows_from_directory(self.comfyui_workflow_dir)
            
            workflows = []
            for wf_data in comfy_workflows:
                # 为每个工作流创建基本的元数据
                workflow_key = wf_data["name"].lower().replace(" ", "_")
                
                # 尝试从工作流JSON中提取有用的信息
                workflow_json = wf_data.get("workflow_json", {})
                
                # 简单的参数提取（可以后续优化）
                editable_params = self._extract_editable_params(workflow_json)
                
                metadata = WorkflowMetadata(
                    name=wf_data["name"],
                    description=f"从ComfyUI加载的工作流: {wf_data['name']}",
                    tags=[WorkflowTag.RDY],  # 默认标记为可用
                    editable_params=editable_params,
                    default_input_dir=f"app/data/inputs/{workflow_key}",
                    default_output_dir=f"app/data/outputs/{workflow_key}"
                )
                
                workflow_info = WorkflowInfo(
                    key=workflow_key,
                    metadata=metadata,
                    workflow_json=workflow_json
                )
                
                workflows.append(workflow_info)
            
            return workflows
            
        except Exception as e:
            print(f"警告：读取ComfyUI工作流失败: {str(e)}")
            return []
    
    def get_workflow(self, workflow_key: str) -> Optional[WorkflowInfo]:
        """获取指定工作流详情"""
        # 由于是异步的，这里需要调用方处理异步
        # 暂时返回None，实际使用时需要修改调用方式
        return None
    
    async def get_workflow_async(self, workflow_key: str) -> Optional[WorkflowInfo]:
        """异步获取指定工作流详情"""
        if not self.comfyui_workflow_dir:
            return None
            
        try:
            workflows = await self.list_workflows()
            return next((w for w in workflows if w.key == workflow_key), None)
        except Exception as e:
            print(f"警告：获取工作流 {workflow_key} 失败: {str(e)}")
            return None
    
    def _extract_editable_params(self, workflow_json: Dict[str, Any]) -> Dict[str, Any]:
        """从工作流JSON中提取可编辑参数"""
        editable_params = {}
        
        try:
            for node_id, node_data in workflow_json.items():
                if isinstance(node_data, dict):
                    node_class = node_data.get("class_type", "")
                    node_inputs = node_data.get("inputs", {})
                    
                    # 识别常见的可编辑参数类型
                    for input_name, input_value in node_inputs.items():
                        if isinstance(input_value, str) and ("text" in input_name.lower() or "prompt" in input_name.lower()):
                            # 文本输入
                            param_name = f"{node_class} - {input_name}"
                            param_path = f"{node_id}.inputs.{input_name}"
                            editable_params[param_name] = param_path
                        elif isinstance(input_value, (int, float)):
                            # 数值输入
                            if "steps" in input_name.lower():
                                param_name = f"{node_class} - {input_name}"
                                param_path = f"{node_id}.inputs.{input_name}"
                                editable_params[param_name] = param_path
                            elif "cfg" in input_name.lower() or "scale" in input_name.lower():
                                param_name = f"{node_class} - {input_name}"
                                param_path = f"{node_id}.inputs.{input_name}"
                                editable_params[param_name] = param_path
        except Exception as e:
            print(f"警告：提取参数失败: {str(e)}")
        
        return editable_params
    
    async def sync_from_comfyui_directory(self, workflow_dir: str) -> Dict[str, Any]:
        """从ComfyUI目录同步工作流"""
        try:
            self.set_comfyui_workflow_dir(workflow_dir)
            workflows = await self.list_workflows()
            
            return {
                "success": True,
                "synced_count": len(workflows),
                "message": f"成功从目录 {workflow_dir} 加载 {len(workflows)} 个工作流"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

# 全局工作流服务实例
workflow_service = WorkflowService()
        
    def list_workflows(self) -> List[WorkflowInfo]:
        """获取所有工作流列表"""
        workflows = []
        
        if not self.workflows_dir.exists():
            return workflows
            
        for workflow_dir in self.workflows_dir.iterdir():
            if workflow_dir.is_dir():
                workflow = self.get_workflow(workflow_dir.name)
                if workflow:
                    workflows.append(workflow)
        
        return workflows
    
    def get_workflow(self, workflow_key: str) -> Optional[WorkflowInfo]:
        """获取指定工作流详情"""
        workflow_path = self.workflows_dir / workflow_key
        
        if not workflow_path.exists() or not workflow_path.is_dir():
            return None
            
        # 读取元数据
        metadata_file = workflow_path / "metadata.json"
        if not metadata_file.exists():
            return None
            
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
                
            # 转换标签
            tags = []
            for tag in metadata_dict.get("tags", ["RDY"]):
                if isinstance(tag, str):
                    try:
                        tags.append(WorkflowTag(tag))
                    except ValueError:
                        # 忽略无效标签
                        pass
                        
            metadata = WorkflowMetadata(
                name=metadata_dict.get("name", workflow_key),
                description=metadata_dict.get("description"),
                tags=tags,
                editable_params=metadata_dict.get("editable_params", {}),
                default_input_dir=metadata_dict.get("default_input_dir"),
                default_output_dir=metadata_dict.get("default_output_dir")
            )
            
            # 读取工作流定义（可选）
            workflow_file = workflow_path / "workflow.json"
            workflow_json = None
            if workflow_file.exists():
                with open(workflow_file, 'r', encoding='utf-8') as f:
                    workflow_json = json.load(f)
            
            return WorkflowInfo(
                key=workflow_key,
                metadata=metadata,
                workflow_json=workflow_json
            )
            
        except Exception as e:
            print(f"警告：加载工作流 {workflow_key} 失败: {str(e)}")
            return None
    
    def create_workflow(self, workflow_key: str, metadata: WorkflowMetadata, workflow_json: Optional[Dict[str, Any]] = None) -> WorkflowInfo:
        """创建新工作流"""
        workflow_path = self.workflows_dir / workflow_key
        workflow_path.mkdir(parents=True, exist_ok=True)
        
        # 写入元数据
        metadata_dict = {
            "name": metadata.name,
            "description": metadata.description,
            "tags": [tag.value for tag in metadata.tags],
            "editable_params": metadata.editable_params,
            "default_input_dir": metadata.default_input_dir,
            "default_output_dir": metadata.default_output_dir
        }
        
        metadata_file = workflow_path / "metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_dict, f, ensure_ascii=False, indent=2)
        
        # 写入工作流定义
        if workflow_json:
            workflow_file = workflow_path / "workflow.json"
            with open(workflow_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_json, f, ensure_ascii=False, indent=2)
        
        return WorkflowInfo(
            key=workflow_key,
            metadata=metadata,
            workflow_json=workflow_json
        )
    
    def update_workflow(self, workflow_key: str, metadata: Optional[WorkflowMetadata] = None, workflow_json: Optional[Dict[str, Any]] = None) -> Optional[WorkflowInfo]:
        """更新工作流"""
        existing = self.get_workflow(workflow_key)
        if not existing:
            return None
        
        if metadata:
            existing.metadata = metadata
        if workflow_json is not None:
            existing.workflow_json = workflow_json
            
        return self.create_workflow(workflow_key, existing.metadata, existing.workflow_json)
    
    def delete_workflow(self, workflow_key: str) -> bool:
        """删除工作流"""
        workflow_path = self.workflows_dir / workflow_key
        
        if not workflow_path.exists():
            return False
            
        import shutil
        shutil.rmtree(workflow_path)
        return True
    
    async def sync_from_comfyui(self) -> Dict[str, Any]:
        """从 ComfyUI 同步工作流"""
        try:
            # 获取 ComfyUI 中的工作流
            comfy_workflows = await comfy_client.get_workflows()
            
            synced_count = 0
            skipped_count = 0
            errors = []
            
            for wf_info in comfy_workflows:
                try:
                    # 生成工作流键名（简化处理）
                    workflow_key = wf_info["name"].lower().replace(" ", "_").replace("-", "_")
                    
                    # 检查是否已存在
                    if self.get_workflow(workflow_key):
                        skipped_count += 1
                        continue
                    
                    # 创建基本的元数据
                    metadata = WorkflowMetadata(
                        name=wf_info["name"],
                        description=wf_info.get("description", f"从 ComfyUI 同步的 {wf_info['name']} 工作流"),
                        tags=[WorkflowTag.DEV],  # 默认标记为开发中
                        editable_params={},  # 需要手动配置
                        default_input_dir=f"app/data/inputs/{workflow_key}",
                        default_output_dir=f"app/data/outputs/{workflow_key}"
                    )
                    
                    # 创建工作流目录结构
                    workflow_path = self.workflows_dir / workflow_key
                    workflow_path.mkdir(parents=True, exist_ok=True)
                    
                    # 保存元数据
                    metadata_dict = {
                        "name": metadata.name,
                        "description": metadata.description,
                        "tags": [tag.value for tag in metadata.tags],
                        "editable_params": metadata.editable_params,
                        "default_input_dir": metadata.default_input_dir,
                        "default_output_dir": metadata.default_output_dir
                    }
                    
                    metadata_file = workflow_path / "metadata.json"
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        json.dump(metadata_dict, f, ensure_ascii=False, indent=2)
                    
                    # 创建空的 workflow.json 作为占位符
                    workflow_file = workflow_path / "workflow.json"
                    with open(workflow_file, 'w', encoding='utf-8') as f:
                        json.dump({}, f, ensure_ascii=False, indent=2)
                    
                    synced_count += 1
                    
                except Exception as e:
                    errors.append(f"同步工作流 {wf_info['name']} 失败: {str(e)}")
            
            return {
                "success": True,
                "synced_count": synced_count,
                "skipped_count": skipped_count,
                "errors": errors
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

# 全局工作流服务实例
workflow_service = WorkflowService()
