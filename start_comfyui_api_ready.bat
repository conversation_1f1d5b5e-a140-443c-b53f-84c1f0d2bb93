@echo off
echo ========================================
echo 🚀 启动ComfyUI (API就绪模式)
echo ========================================

cd /d "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

echo 📋 设置环境变量以修复进度条问题...
REM 禁用tqdm进度条 - 这是导致OSError的主要原因
set TQDM_DISABLE=1

REM 设置Python IO编码
set PYTHONIOENCODING=utf-8

REM 禁用ComfyUI的一些日志功能
set COMFYUI_DISABLE_LOGGING=1

echo ✅ 环境变量设置完成:
echo   TQDM_DISABLE=1 (禁用进度条)
echo   PYTHONIOENCODING=utf-8 (修复编码)
echo   COMFYUI_DISABLE_LOGGING=1 (禁用问题日志)

echo.
echo 🔥 启动ComfyUI服务器...
echo 💡 关键参数说明:
echo   --disable-console-progress-bar : 禁用控制台进度条(修复OSError)
echo   --listen 127.0.0.1 : 监听本地地址
echo   --port 8188 : API端口
echo   --disable-auto-launch : 不自动打开浏览器

python main.py ^
  --user-directory "C:\Users\<USER>\Documents\ComfyUI\user" ^
  --input-directory "C:\Users\<USER>\Documents\ComfyUI\input" ^
  --output-directory "C:\Users\<USER>\Documents\ComfyUI\output" ^
  --listen 127.0.0.1 ^
  --port 8188 ^
  --disable-auto-launch ^
  --disable-console-progress-bar

echo.
echo 🏁 ComfyUI已退出
pause
