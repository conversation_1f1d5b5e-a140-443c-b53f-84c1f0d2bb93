#!/usr/bin/env python3
"""
最终ComfyUI测试 - 完整监控执行过程
"""

import requests
import json
import time
import os

COMFYUI_BASE_URL = "http://localhost:8188"

def test_with_monitoring():
    """测试并完整监控执行过程"""
    print("🧪 最终测试 - 完整监控")
    
    # 创建最简单的工作流
    workflow = {
        "3": {
            "inputs": {
                "seed": 123456,
                "steps": 3,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "4": {
            "inputs": {
                "ckpt_name": "SDXL\\sdXL_v10VAEFix.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "5": {
            "inputs": {
                "width": 256,
                "height": 256,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "text": "test",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "7": {
            "inputs": {
                "text": "",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode"
        },
        "9": {
            "inputs": {
                "filename_prefix": "final_test",
                "images": ["8", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    # 提交工作流
    try:
        payload = {
            "client_id": "final_test",
            "prompt": workflow
        }
        
        print("📤 提交工作流...")
        response = requests.post(f"{COMFYUI_BASE_URL}/prompt", json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"✅ 提交成功: {prompt_id}")
            
            if node_errors:
                print(f"❌ 节点错误: {node_errors}")
                return False
            
            # 详细监控执行过程
            return monitor_detailed_execution(prompt_id)
        else:
            print(f"❌ 提交失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def monitor_detailed_execution(prompt_id, max_wait=120):
    """详细监控执行过程"""
    print(f"⏳ 详细监控执行过程 (prompt_id: {prompt_id})...")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            # 检查队列状态
            queue_response = requests.get(f"{COMFYUI_BASE_URL}/queue", timeout=5)
            if queue_response.status_code == 200:
                queue_data = queue_response.json()
                running = queue_data.get("queue_running", [])
                pending = queue_data.get("queue_pending", [])
                
                if running:
                    print(f"🔄 正在执行: {len(running)} 个任务")
                if pending:
                    print(f"⏳ 等待中: {len(pending)} 个任务")
            
            # 检查历史记录
            history_response = requests.get(f"{COMFYUI_BASE_URL}/history/{prompt_id}", timeout=5)
            if history_response.status_code == 200:
                history = history_response.json()
                
                if prompt_id in history:
                    status = history[prompt_id].get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)
                    
                    if status_str != last_status:
                        print(f"📊 状态变化: {last_status} → {status_str}")
                        last_status = status_str
                    
                    if completed:
                        if status_str == "success":
                            print("🎉 执行成功！")
                            outputs = history[prompt_id].get("outputs", {})
                            print(f"📁 输出节点: {list(outputs.keys())}")
                            
                            # 检查输出文件
                            for node_id, output_data in outputs.items():
                                if "images" in output_data:
                                    images = output_data["images"]
                                    print(f"  节点{node_id}生成了{len(images)}张图片:")
                                    for img in images:
                                        print(f"    📷 {img.get('filename', '未知文件名')}")
                            
                            return True
                        else:
                            print(f"❌ 执行失败: {status_str}")
                            
                            # 详细错误分析
                            messages = status.get("messages", [])
                            for msg in messages:
                                if isinstance(msg, list) and len(msg) >= 2:
                                    msg_type = msg[0]
                                    msg_data = msg[1]
                                    
                                    if msg_type == "execution_error":
                                        print(f"  🔍 错误详情:")
                                        print(f"    节点: {msg_data.get('node_id')} ({msg_data.get('node_type')})")
                                        print(f"    异常: {msg_data.get('exception_type')}")
                                        print(f"    消息: {msg_data.get('exception_message')}")
                                        
                                        # 分析具体错误
                                        exception_msg = msg_data.get('exception_message', '')
                                        if '[Errno 22] Invalid argument' in exception_msg:
                                            print(f"  💡 这是进度条问题！ComfyUI没有使用修复参数启动")
                                        elif 'No such file' in exception_msg:
                                            print(f"  💡 这是文件路径问题")
                                        elif 'Permission denied' in exception_msg:
                                            print(f"  💡 这是权限问题")
                            
                            return False
                    
                    if status_str == "error":
                        print("❌ 执行出错")
                        return False
            
            time.sleep(3)
            print(".", end="", flush=True)
            
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")
            break
    
    print(f"\n⏰ 监控超时")
    return False

def main():
    """主流程"""
    print("=" * 60)
    print("🎯 最终ComfyUI测试")
    print("=" * 60)
    
    # 检查ComfyUI连接
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/system_stats", timeout=5)
        if response.status_code != 200:
            print("❌ ComfyUI未运行，请先启动")
            print("💡 建议使用: python direct_comfyui_test.py")
            return
    except:
        print("❌ ComfyUI未运行，请先启动")
        return
    
    print("✅ ComfyUI运行中，开始测试...")
    
    # 执行测试
    success = test_with_monitoring()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！")
        print("✅ 端口占用问题澄清：不存在端口冲突")
        print("✅ ComfyUI可以通过API正常调用")
        print("✅ 你的架构设计完全正确")
    else:
        print("❌ 测试失败")
        print("💡 如果看到 '[Errno 22] Invalid argument'")
        print("   说明ComfyUI仍未使用修复参数启动")
    print("=" * 60)

if __name__ == "__main__":
    main()
