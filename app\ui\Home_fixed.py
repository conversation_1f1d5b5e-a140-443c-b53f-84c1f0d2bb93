import streamlit as st
import httpx
import asyncio
from typing import Dict, Any, Optional, List
import os
from datetime import datetime
import json
import subprocess
import tkinter as tk
from tkinter import filedialog
import threading

# 页面配置
st.set_page_config(
    page_title="FreemanWorkHub",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API 基础地址
API_BASE_URL = f"http://localhost:{os.getenv('API_PORT', '8001')}"

# 异步请求辅助函数
async def api_request(method: str, endpoint: str, headers: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
    """发送 API 请求"""
    url = f"{API_BASE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                # 认证失败，清除登录状态
                if "logged_in" in st.session_state:
                    del st.session_state.logged_in
                    del st.session_state.token
                    del st.session_state.user_info
                st.error("登录已过期，请重新登录")
                st.stop()
            else:
                st.error(f"API 请求失败: {e.response.status_code} - {e.response.text}")
                return {"success": False, "error": e.response.text}
        except Exception as e:
            st.error(f"网络错误: {str(e)}")
            return {"success": False, "error": str(e)}

def get_auth_headers() -> Dict[str, str]:
    """获取认证头"""
    if "token" in st.session_state:
        return {"Authorization": f"Bearer {st.session_state.token}"}
    return {}

def select_folder_with_dialog():
    """使用系统文件对话框选择文件夹"""
    try:
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        root.attributes('-topmost', True)  # 置顶显示
        
        # 打开文件夹选择对话框
        folder_path = filedialog.askdirectory(
            title="选择 ComfyUI 工作流目录",
            initialdir=os.path.expanduser("~")
        )
        
        # 清理tkinter资源
        root.destroy()
        
        return folder_path if folder_path else None
        
    except Exception as e:
        print(f"文件夹选择失败: {e}")
        return None

def show_login_page():
    """显示登录页面"""
    st.title("🔐 登录 FreemanWorkHub")
    
    with st.form("login_form"):
        username = st.text_input("用户名")
        access_key = st.text_input("访问密钥", type="password")
        
        if st.form_submit_button("登录", type="primary", use_container_width=True):
            if username and access_key:
                with st.spinner("正在验证..."):
                    login_data = {"username": username, "access_key": access_key}
                    
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(
                            api_request("POST", "/auth/login", json=login_data)
                        )
                        loop.close()
                        
                        if result.get("success"):
                            st.session_state.logged_in = True
                            st.session_state.token = result["token"]
                            st.session_state.user_info = result["user"]
                            st.success("登录成功！")
                            st.rerun()
                        else:
                            st.error("登录失败，请检查用户名和访问密钥")
                    except Exception as e:
                        st.error(f"登录请求失败: {str(e)}")
            else:
                st.error("请输入用户名和访问密钥")

def show_home_page():
    """显示主页"""
    st.title("🏠 FreemanWorkHub")
    
    # 检查登录状态
    if "logged_in" not in st.session_state or not st.session_state.logged_in:
        show_login_page()
        return
    
    # 显示用户信息
    col1, col2 = st.columns([3, 1])
    with col1:
        username = st.session_state.user_info.get("username", "用户")
        st.subheader(f"欢迎回来，{username}!")
    with col2:
        if st.button("🚪 退出登录"):
            del st.session_state.logged_in
            del st.session_state.token
            del st.session_state.user_info
            st.rerun()
    
    # 检查API连接状态
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        health = loop.run_until_complete(api_request("GET", "/health"))
        loop.close()
        
        if health.get("success"):
            st.success("✅ API 服务连接正常")
        else:
            st.error("❌ API 服务连接异常")
    except:
        st.warning("⚠️ 无法连接到API服务，请检查后端是否运行")
    
    # 工作流Gallery
    st.markdown("---")
    st.subheader("🎨 工作流 Gallery")
    
    # 获取工作流列表
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        workflows_result = loop.run_until_complete(
            api_request("GET", "/workflows", headers=get_auth_headers())
        )
        loop.close()
        
        if workflows_result.get("success"):
            workflows = workflows_result.get("workflows", [])
            
            if workflows:
                # 按标签分组显示
                dev_workflows = [w for w in workflows if w.get("tags", []) and "DEV" in w["tags"]]
                rdy_workflows = [w for w in workflows if w.get("tags", []) and "RDY" in w["tags"]]
                
                # RDY工作流（可用）
                if rdy_workflows:
                    st.markdown("### ✅ 可用工作流")
                    cols = st.columns(3)
                    for i, workflow in enumerate(rdy_workflows):
                        with cols[i % 3]:
                            with st.container():
                                st.markdown(f"**{workflow['name']}**")
                                st.caption(workflow.get("description", ""))
                                if st.button("运行", key=f"run_{workflow['key']}", use_container_width=True):
                                    st.session_state.selected_workflow = workflow
                                    st.session_state.page = "workflow_detail"
                                    st.rerun()
                
                # DEV工作流（开发中）
                if dev_workflows:
                    with st.expander("🔧 开发中工作流 (DEV)"):
                        cols = st.columns(3)
                        for i, workflow in enumerate(dev_workflows):
                            with cols[i % 3]:
                                with st.container():
                                    st.markdown(f"**{workflow['name']}**")
                                    st.caption(workflow.get("description", ""))
                                    st.info("开发中 - 仅供查看")
            else:
                st.info("📂 暂无工作流，请先配置工作流目录")
                if st.button("⚙️ 前往工作流管理", use_container_width=True):
                    st.session_state.page = "workflows"
                    st.rerun()
        else:
            st.error("获取工作流列表失败")
    except Exception as e:
        st.error(f"获取工作流列表时出错: {str(e)}")

def show_workflows_page():
    """显示工作流管理页面"""
    st.title("⚙️ 工作流管理")
    
    # 检查是否已设置ComfyUI工作流目录
    if "comfyui_workflow_dir" not in st.session_state:
        st.session_state.comfyui_workflow_dir = ""
    
    # 目录设置区域
    with st.expander("📁 ComfyUI 工作流目录设置", expanded=(st.session_state.comfyui_workflow_dir == "")):
        # 显示当前设置的目录
        if st.session_state.comfyui_workflow_dir:
            st.success(f"✅ 当前工作流目录: `{st.session_state.comfyui_workflow_dir}`")
            
            # 提供一些快捷操作
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("🔄 重新选择目录", use_container_width=True):
                    st.session_state.comfyui_workflow_dir = ""
                    st.rerun()
            with col2:
                if st.button("📂 打开目录", use_container_width=True):
                    try:
                        if os.name == "nt":  # Windows
                            os.startfile(st.session_state.comfyui_workflow_dir)
                        else:  # Linux/Mac
                            subprocess.run(["xdg-open", st.session_state.comfyui_workflow_dir])
                        st.info("已在文件管理器中打开目录")
                    except:
                        st.warning("无法打开目录，请手动导航")
            with col3:
                if st.button("🔍 查看文件", use_container_width=True):
                    try:
                        files = os.listdir(st.session_state.comfyui_workflow_dir)
                        json_files = [f for f in files if f.endswith(".json")]
                        file_list = "', '".join(json_files[:5])
                        if len(json_files) > 5:
                            file_list += "..."
                        st.info(f"目录中找到 {len(json_files)} 个JSON文件: {file_list}")
                    except:
                        st.error("无法读取目录内容")
        else:
            st.info("请点击下方按钮打开文件夹选择对话框")
            
            # 文件夹选择按钮
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📁 浏览并选择文件夹", type="primary", use_container_width=True):
                    # 使用文件对话框选择文件夹
                    selected_folder = select_folder_with_dialog()
                    if selected_folder:
                        st.session_state.comfyui_workflow_dir = selected_folder
                        st.success(f"已选择目录: `{selected_folder}`")
                        st.rerun()
                    else:
                        st.info("未选择目录")
            
            with col2:
                if st.button("🔍 自动检测", use_container_width=True):
                    # 自动检测常见的ComfyUI路径
                    detected_paths = []
                    search_paths = [
                        "~/Documents/ComfyUI/user/default/workflows",
                        "~/Documents/ComfyUI/user/workflows", 
                        "~/ComfyUI/workflows",
                        "~/AI/ComfyUI/workflows"
                    ]
                    
                    for path_template in search_paths:
                        actual_path = os.path.expanduser(path_template)
                        if os.path.exists(actual_path):
                            detected_paths.append(actual_path)
                    
                    if detected_paths:
                        st.success(f"检测到 {len(detected_paths)} 个可能的工作流目录")
                        for path in detected_paths:
                            if st.button(f"选择: {path}", key=f"detected_{path}"):
                                st.session_state.comfyui_workflow_dir = path
                                st.success(f"已选择目录: `{path}`")
                                st.rerun()
                    else:
                        st.warning("未检测到ComfyUI工作流目录，请手动选择")
            
            # 手动输入路径
            st.markdown("---")
            manual_path = st.text_input(
                "或手动输入目录路径",
                placeholder="粘贴或输入ComfyUI工作流目录的完整路径",
                help="例如: C:\\Users\\<USER>\\Documents\\ComfyUI\\user\\workflows"
            )
            
            if manual_path and st.button("✅ 使用此路径", use_container_width=True):
                if os.path.exists(manual_path) and os.path.isdir(manual_path):
                    st.session_state.comfyui_workflow_dir = manual_path
                    st.success(f"已设置目录: `{manual_path}`")
                    st.rerun()
                else:
                    st.error("路径不存在或不是有效目录，请检查路径是否正确")
    
    # 同步按钮和刷新按钮
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.markdown("#### 工作流列表")
    
    with col2:
        if st.button("🔄 同步工作流", use_container_width=True, 
                    disabled=(st.session_state.comfyui_workflow_dir == ""),
                    help="从指定目录同步工作流" if st.session_state.comfyui_workflow_dir else "请先设置工作流目录"):
            if st.session_state.comfyui_workflow_dir:
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    sync_result = loop.run_until_complete(
                        api_request("POST", "/workflows/sync", 
                                  headers=get_auth_headers(), 
                                  json={"workflow_dir": st.session_state.comfyui_workflow_dir})
                    )
                    loop.close()
                    
                    if sync_result.get("success"):
                        processed = sync_result.get("processed", 0)
                        st.success(f"✅ 同步完成！共处理 {processed} 个工作流")
                        st.rerun()
                    else:
                        error_msg = sync_result.get("error", "未知错误")
                        st.error(f"同步失败: {error_msg}")
                except Exception as e:
                    st.error(f"同步过程中出错: {str(e)}")
    
    with col3:
        if st.button("🔄 刷新列表", use_container_width=True):
            st.rerun()
    
    # 获取并显示工作流列表
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        workflows_result = loop.run_until_complete(
            api_request("GET", "/workflows", headers=get_auth_headers())
        )
        loop.close()
        
        if workflows_result.get("success"):
            workflows = workflows_result.get("workflows", [])
            
            if workflows:
                # 创建选项卡
                tab1, tab2 = st.tabs(["📋 所有工作流", "🏷️ 按标签分组"])
                
                with tab1:
                    # 显示所有工作流
                    for workflow in workflows:
                        with st.expander(f"{workflow['name']} ({workflow['key']})"):
                            col1, col2, col3 = st.columns([2, 1, 1])
                            
                            with col1:
                                description = workflow.get("description", "无描述")
                                tags = ", ".join(workflow.get("tags", []))
                                file_path = workflow.get("file_path", "未知")
                                st.markdown(f"**描述:** {description}")
                                st.markdown(f"**标签:** {tags}")
                                st.markdown(f"**文件:** {file_path}")
                            
                            with col2:
                                if st.button("运行", key=f"run_{workflow['key']}", use_container_width=True):
                                    st.session_state.selected_workflow = workflow
                                    st.session_state.page = "workflow_detail"
                                    st.rerun()
                            
                            with col3:
                                if st.button("编辑", key=f"edit_{workflow['key']}", use_container_width=True):
                                    st.session_state.selected_workflow = workflow
                                    st.session_state.page = "workflow_editor"
                                    st.rerun()
                
                with tab2:
                    # 按标签分组显示
                    dev_workflows = [w for w in workflows if w.get("tags", []) and "DEV" in w["tags"]]
                    rdy_workflows = [w for w in workflows if w.get("tags", []) and "RDY" in w["tags"]]
                    
                    if rdy_workflows:
                        st.markdown("### ✅ 可用工作流 (RDY)")
                        for workflow in rdy_workflows:
                            with st.expander(workflow["name"]):
                                description = workflow.get("description", "无描述")
                                st.markdown(f"**描述:** {description}")
                                if st.button("运行", key=f"run_rdy_{workflow['key']}", use_container_width=True):
                                    st.session_state.selected_workflow = workflow
                                    st.session_state.page = "workflow_detail"
                                    st.rerun()
                    
                    if dev_workflows:
                        st.markdown("### 🔧 开发中工作流 (DEV)")
                        for workflow in dev_workflows:
                            with st.expander(workflow["name"]):
                                description = workflow.get("description", "无描述")
                                st.markdown(f"**描述:** {description}")
                                st.info("此工作流正在开发中，仅供查看")
            else:
                st.info("📂 暂无工作流，请先同步工作流目录")
        else:
            st.error("获取工作流列表失败")
    except Exception as e:
        st.error(f"获取工作流列表时出错: {str(e)}")

def show_workflow_detail_page():
    """显示工作流详情页面"""
    st.title("🎯 工作流详情")
    
    if "selected_workflow" not in st.session_state:
        st.error("未选择工作流")
        if st.button("返回工作流列表"):
            st.session_state.page = "workflows"
            st.rerun()
        return
    
    workflow = st.session_state.selected_workflow
    
    # 工作流基本信息
    st.subheader(workflow["name"])
    description = workflow.get("description", "无描述")
    tags = ", ".join(workflow.get("tags", []))
    st.markdown(f"**描述:** {description}")
    st.markdown(f"**标签:** {tags}")
    
    # 参数设置
    st.markdown("---")
    st.subheader("⚙️ 参数设置")
    
    # 这里可以添加参数编辑界面
    st.info("参数编辑功能开发中...")
    
    # 运行控制
    st.markdown("---")
    st.subheader("🚀 运行控制")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("▶️ 开始运行", type="primary", use_container_width=True):
            st.info("运行功能开发中...")
    
    with col2:
        if st.button("🔙 返回列表", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()

def show_pipeline_page():
    """显示流水线编排页面"""
    st.title("🔗 流水线编排")
    
    st.info("流水线编排功能开发中...")
    if st.button("返回主页"):
        st.session_state.page = "home"
        st.rerun()

def main():
    """主函数"""
    # 初始化页面状态
    if "page" not in st.session_state:
        st.session_state.page = "home"
    
    # 侧边栏导航
    with st.sidebar:
        st.title("🧭 导航")
        
        if st.button("🏠 主页", use_container_width=True):
            st.session_state.page = "home"
        
        if st.button("⚙️ 工作流管理", use_container_width=True):
            st.session_state.page = "workflows"
        
        if st.button("🔗 流水线编排", use_container_width=True):
            st.session_state.page = "pipeline"
        
        if st.button("📊 任务监控", use_container_width=True):
            st.session_state.page = "monitor"
        
        if st.button("📁 输出管理", use_container_width=True):
            st.session_state.page = "outputs"
    
    # 页面路由
    if st.session_state.page == "home":
        show_home_page()
    elif st.session_state.page == "workflows":
        show_workflows_page()
    elif st.session_state.page == "workflow_detail":
        show_workflow_detail_page()
    elif st.session_state.page == "workflow_editor":
        st.title("✏️ 工作流编辑器")
        st.info("编辑器功能开发中...")
    elif st.session_state.page == "pipeline":
        show_pipeline_page()
    elif st.session_state.page == "monitor":
        st.title("📊 任务监控")
        st.info("监控功能开发中...")
    elif st.session_state.page == "outputs":
        st.title("📁 输出管理")
        st.info("输出管理功能开发中...")
    else:
        show_home_page()

if __name__ == "__main__":
    main()
