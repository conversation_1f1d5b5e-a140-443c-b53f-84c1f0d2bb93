#!/usr/bin/env python3
"""
测试ComfyUI进度条修复效果
验证工作流是否能正常执行
"""

import json
import requests
import time
from pathlib import Path
import sys

# ComfyUI服务器配置
COMFYUI_BASE_URL = "http://localhost:8188"
CLIENT_ID = "freemanhub"

def check_comfyui_connection():
    """检查ComfyUI连接状态"""
    print("🔍 检查ComfyUI连接状态...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/system_stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ ComfyUI服务器运行正常")
            print(f"  版本: {stats.get('system', {}).get('comfyui_version', '未知')}")
            return True
        else:
            print(f"❌ ComfyUI服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到ComfyUI服务器: {e}")
        return False

def create_simple_test_workflow():
    """创建一个简单的测试工作流"""
    print("🧪 创建简单测试工作流...")
    
    # 创建一个最简单的文生图工作流
    simple_workflow = {
        "3": {
            "inputs": {
                "seed": 123456,
                "steps": 10,  # 减少步数加快测试
                "cfg": 7.0,
                "sampler_name": "euler",  # 使用更稳定的采样器
                "scheduler": "normal",    # 使用普通调度器
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "4": {
            "inputs": {
                "ckpt_name": "SDXL\\sdXL_v10VAEFix.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "5": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "text": "a simple test image, best quality",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "7": {
            "inputs": {
                "text": "blurry, bad quality",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode"
        },
        "9": {
            "inputs": {
                "filename_prefix": "test_fix",
                "images": ["8", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    return simple_workflow

def test_workflow_submission(workflow_json):
    """测试工作流提交"""
    print("🚀 测试工作流提交...")
    
    try:
        payload = {
            "client_id": CLIENT_ID,
            "prompt": workflow_json
        }
        
        print(f"📤 发送请求到: {COMFYUI_BASE_URL}/prompt")
        response = requests.post(
            f"{COMFYUI_BASE_URL}/prompt",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"✅ 工作流提交成功")
            print(f"  Prompt ID: {prompt_id}")
            
            if node_errors:
                print(f"❌ 发现节点错误:")
                for node_id, errors in node_errors.items():
                    print(f"    节点 {node_id}: {errors}")
                return None
            else:
                print(f"✅ 无节点错误，工作流开始执行")
                return prompt_id
        else:
            print(f"❌ 工作流提交失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 提交工作流时出错: {e}")
        return None

def monitor_execution(prompt_id, max_wait_time=60):
    """监控工作流执行状态"""
    print(f"⏳ 监控执行状态 (最多等待{max_wait_time}秒)...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            # 检查历史记录
            response = requests.get(f"{COMFYUI_BASE_URL}/history/{prompt_id}", timeout=5)
            if response.status_code == 200:
                history = response.json()
                
                if prompt_id in history:
                    status = history[prompt_id].get("status", {})
                    status_str = status.get("status_str", "unknown")
                    completed = status.get("completed", False)
                    
                    print(f"📊 状态: {status_str}, 完成: {completed}")
                    
                    if completed:
                        if status_str == "success":
                            print("🎉 工作流执行成功！")
                            outputs = history[prompt_id].get("outputs", {})
                            print(f"📁 输出节点数: {len(outputs)}")
                            return True
                        else:
                            print(f"❌ 工作流执行失败: {status_str}")
                            messages = status.get("messages", [])
                            for msg in messages[-3:]:  # 显示最后3条消息
                                print(f"  {msg}")
                            return False
                    
                    # 如果状态是error，直接返回失败
                    if status_str == "error":
                        print(f"❌ 工作流执行出错")
                        messages = status.get("messages", [])
                        for msg in messages[-2:]:  # 显示最后2条错误消息
                            if isinstance(msg, list) and len(msg) >= 2:
                                msg_type = msg[0]
                                msg_data = msg[1]
                                if msg_type == "execution_error":
                                    print(f"  错误节点: {msg_data.get('node_id')} ({msg_data.get('node_type')})")
                                    print(f"  错误信息: {msg_data.get('exception_message', '未知错误')}")
                        return False
            
            # 等待一段时间再检查
            time.sleep(2)
            print(".", end="", flush=True)
            
        except Exception as e:
            print(f"\n❌ 监控过程出错: {e}")
            break
    
    print(f"\n⏰ 监控超时 ({max_wait_time}秒)")
    return False

def main():
    """主测试流程"""
    print("=" * 60)
    print("🧪 ComfyUI进度条修复测试")
    print("=" * 60)
    
    # 1. 检查连接
    if not check_comfyui_connection():
        print("\n❌ 请确保ComfyUI正在运行")
        print("💡 建议使用修复后的启动脚本: start_comfyui_fixed.bat")
        return
    
    # 2. 创建测试工作流
    test_workflow = create_simple_test_workflow()
    
    # 3. 提交测试
    prompt_id = test_workflow_submission(test_workflow)
    
    if not prompt_id:
        print("\n❌ 工作流提交失败")
        return
    
    # 4. 监控执行
    success = monitor_execution(prompt_id, max_wait_time=120)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！ComfyUI进度条问题已修复")
        print("💡 现在可以正常通过API调用ComfyUI了")
    else:
        print("❌ 测试失败，可能需要进一步调试")
        print("💡 建议检查:")
        print("  1. 模型文件是否存在")
        print("  2. ComfyUI是否使用了修复后的启动参数")
        print("  3. 是否还有其他环境问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
