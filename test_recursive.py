import asyncio
import pytest
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath('.'))

from app.api.comfy_client import comfy_client

def test_recursive_search():
    def _run():
        return asyncio.run(comfy_client.get_workflows_from_directory(r'C:\\Users\\<USER>\\Documents\\FreemanWorkHub\\test_comfyui_workflows'))
    try:
        workflows = _run()
        assert isinstance(workflows, list)
        # 至少应找到 1 个示例工作流目录
        assert len(workflows) >= 1
    except Exception as e:
        # 测试环境下若路径不存在，允许为 0，但不抛异常
        pytest.skip(f"跳过：环境不可用 {e}")

if __name__ == "__main__":
    asyncio.run(test_recursive_search())
