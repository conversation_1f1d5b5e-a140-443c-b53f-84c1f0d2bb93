#!/usr/bin/env python3
"""
深度诊断ComfyUI问题
检查模型文件、路径、权限等
"""

import requests
import json
import os
from pathlib import Path

COMFYUI_BASE_URL = "http://localhost:8188"

def check_model_files():
    """检查模型文件是否存在"""
    print("🔍 检查模型文件...")
    
    # ComfyUI模型目录
    model_dirs = [
        r"C:\Users\<USER>\Documents\ComfyUI\models\checkpoints",
        r"C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\models\checkpoints"
    ]
    
    target_model = "SDXL\\sdXL_v10VAEFix.safetensors"
    
    for model_dir in model_dirs:
        model_path = Path(model_dir) / "SDXL" / "sdXL_v10VAEFix.safetensors"
        print(f"  检查: {model_path}")
        
        if model_path.exists():
            print(f"  ✅ 找到模型文件: {model_path}")
            print(f"     大小: {model_path.stat().st_size / (1024*1024*1024):.1f} GB")
            return str(model_path)
        else:
            print(f"  ❌ 模型文件不存在")
    
    print("❌ 未找到SDXL模型文件")
    return None

def check_lora_files():
    """检查LoRA文件"""
    print("\n🔍 检查LoRA文件...")
    
    lora_dirs = [
        r"C:\Users\<USER>\Documents\ComfyUI\models\loras",
        r"C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\models\loras"
    ]
    
    target_lora = "SDXL\\logomkrdsxl.safetensors"
    
    for lora_dir in lora_dirs:
        lora_path = Path(lora_dir) / "SDXL" / "logomkrdsxl.safetensors"
        print(f"  检查: {lora_path}")
        
        if lora_path.exists():
            print(f"  ✅ 找到LoRA文件: {lora_path}")
            return str(lora_path)
        else:
            print(f"  ❌ LoRA文件不存在")
    
    print("❌ 未找到LoRA文件")
    return None

def get_available_models():
    """获取ComfyUI中可用的模型"""
    print("\n🔍 获取ComfyUI可用模型...")
    
    try:
        response = requests.get(f"{COMFYUI_BASE_URL}/object_info", timeout=10)
        if response.status_code == 200:
            object_info = response.json()
            
            # 获取CheckpointLoaderSimple的可用模型
            checkpoint_loader = object_info.get("CheckpointLoaderSimple", {})
            input_info = checkpoint_loader.get("input", {})
            ckpt_name_info = input_info.get("ckpt_name", {})
            
            if isinstance(ckpt_name_info, list) and len(ckpt_name_info) > 1:
                available_models = ckpt_name_info[0]  # 第一个元素通常是可用选项列表
                print(f"✅ 找到 {len(available_models)} 个可用模型:")
                for i, model in enumerate(available_models[:5]):  # 只显示前5个
                    print(f"  {i+1}. {model}")
                if len(available_models) > 5:
                    print(f"  ... 还有 {len(available_models)-5} 个模型")
                return available_models
            else:
                print("❌ 无法获取模型列表")
                return []
        else:
            print(f"❌ 获取模型信息失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取模型信息时出错: {e}")
        return []

def create_minimal_workflow(model_name=None):
    """创建最小化工作流"""
    print("\n🧪 创建最小化测试工作流...")
    
    # 使用可用的模型，或者默认模型
    if not model_name:
        model_name = "SDXL\\sdXL_v10VAEFix.safetensors"
    
    print(f"  使用模型: {model_name}")
    
    # 最简单的工作流，不使用LoRA
    minimal_workflow = {
        "3": {
            "inputs": {
                "seed": 123456,
                "steps": 3,  # 极少步数
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal", 
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "4": {
            "inputs": {
                "ckpt_name": model_name
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "5": {
            "inputs": {
                "width": 256,  # 更小的尺寸
                "height": 256,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "text": "test",  # 最简单的提示词
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "7": {
            "inputs": {
                "text": "",  # 空的负面提示词
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode"
        },
        "9": {
            "inputs": {
                "filename_prefix": "minimal_test",
                "images": ["8", 0]
            },
            "class_type": "SaveImage"
        }
    }
    
    return minimal_workflow

def test_minimal_workflow(workflow):
    """测试最小化工作流"""
    print("🚀 测试最小化工作流...")
    
    try:
        payload = {
            "client_id": "minimal_test",
            "prompt": workflow
        }
        
        response = requests.post(f"{COMFYUI_BASE_URL}/prompt", json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            prompt_id = result.get("prompt_id")
            node_errors = result.get("node_errors", {})
            
            print(f"✅ 提交成功: {prompt_id}")
            
            if node_errors:
                print(f"❌ 节点错误:")
                for node_id, errors in node_errors.items():
                    print(f"  节点{node_id}: {errors}")
                return False
            
            return prompt_id
        else:
            print(f"❌ 提交失败: {response.status_code}")
            print(f"  响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主诊断流程"""
    print("=" * 60)
    print("🔬 ComfyUI问题深度诊断")
    print("=" * 60)
    
    # 1. 检查模型文件
    model_path = check_model_files()
    
    # 2. 检查LoRA文件
    lora_path = check_lora_files()
    
    # 3. 获取可用模型
    available_models = get_available_models()
    
    # 4. 选择可用的模型进行测试
    test_model = None
    if available_models:
        test_model = available_models[0]  # 使用第一个可用模型
        print(f"\n💡 将使用可用模型进行测试: {test_model}")
    
    # 5. 创建并测试最小化工作流
    minimal_workflow = create_minimal_workflow(test_model)
    result = test_minimal_workflow(minimal_workflow)
    
    print("\n" + "=" * 60)
    print("📋 诊断结果:")
    print(f"  模型文件: {'✅' if model_path else '❌'}")
    print(f"  LoRA文件: {'✅' if lora_path else '❌'}")
    print(f"  可用模型: {len(available_models)} 个")
    print(f"  工作流测试: {'✅' if result else '❌'}")
    
    if not result:
        print("\n💡 建议:")
        print("  1. 确保ComfyUI使用了 --disable-console-progress-bar 参数")
        print("  2. 检查模型文件是否正确安装")
        print("  3. 尝试在ComfyUI界面中手动运行相同工作流")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
