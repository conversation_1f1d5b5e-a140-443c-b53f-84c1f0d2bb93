@echo off
echo 🚀 启动ComfyUI (禁用Manager)
cd /d "C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI"

REM 临时重命名ComfyUI-Manager目录
if exist "custom_nodes\ComfyUI-Manager" (
    echo 📦 临时禁用ComfyUI-Manager...
    ren "custom_nodes\ComfyUI-Manager" "ComfyUI-Manager.disabled"
)

REM 启动ComfyUI
echo 🔥 启动ComfyUI...
python main.py --user-directory "C:\Users\<USER>\Documents\ComfyUI\user" --input-directory "C:\Users\<USER>\Documents\ComfyUI\input" --output-directory "C:\Users\<USER>\Documents\ComfyUI\output" --listen 127.0.0.1 --port 8188

REM 恢复ComfyUI-Manager
if exist "custom_nodes\ComfyUI-Manager.disabled" (
    echo 📦 恢复ComfyUI-Manager...
    ren "custom_nodes\ComfyUI-Manager.disabled" "ComfyUI-Manager"
)

pause
